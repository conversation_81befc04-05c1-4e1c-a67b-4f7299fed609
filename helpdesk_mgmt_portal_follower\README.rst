===============
Portal Follower
===============

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:4c468b603bc94823805521b28b037299d05c9bfcc41a94005091299319d4732b
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fhelpdesk-lightgray.png?logo=github
    :target: https://github.com/OCA/helpdesk/tree/18.0/helpdesk_mgmt_portal_follower
    :alt: OCA/helpdesk
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/helpdesk-18-0/helpdesk-18-0-helpdesk_mgmt_portal_follower
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/helpdesk&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module adds the posibilty to add followers to a helpdesk ticket
from the helpdesk form at the website portal.

**Table of contents**

.. contents::
   :local:

Usage
=====

To make it work you just need to add the emails of the wanted followers
to the ``Emails in Copy`` input splitted by comas.

Known issues / Roadmap
======================

Known Caveats
-------------

1. **Spam Risks**:

   - Allowing users to input any email address could lead to spam if
     unintended recipients are included.

2. **Privacy Concerns**:

   - Users may inadvertently share sensitive information by adding
     followers without the consent of the email address owners.
   - This feature could expose email addresses to individuals who should
     not have access to that information.

3. **User Misunderstanding**:

   - End-users may not fully understand the implications of adding
     multiple email addresses, potentially leading to confusion or
     misuse.

If you plan to use this module, it is essential to inform users about
the importance of carefully selecting followers to add. This process
will help maintain privacy and limit unnecessary notifications.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/helpdesk/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/helpdesk/issues/new?body=module:%20helpdesk_mgmt_portal_follower%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Advanced Programming Solutions S.L. - APSL - Nagarro
* Bernat Obrador
* Patryk Pyczko
* Miquel Pascual

Contributors
------------

- `APSL-Nagarro <https://apsl.tech>`__:

  - <NAME_EMAIL>
  - <NAME_EMAIL>
  - <NAME_EMAIL>
  - <NAME_EMAIL>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-BernatObrador| image:: https://github.com/BernatObrador.png?size=40px
    :target: https://github.com/BernatObrador
    :alt: BernatObrador
.. |maintainer-ppyczko| image:: https://github.com/ppyczko.png?size=40px
    :target: https://github.com/ppyczko
    :alt: ppyczko
.. |maintainer-mpascuall| image:: https://github.com/mpascuall.png?size=40px
    :target: https://github.com/mpascuall
    :alt: mpascuall

Current `maintainers <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-BernatObrador| |maintainer-ppyczko| |maintainer-mpascuall| 

This module is part of the `OCA/helpdesk <https://github.com/OCA/helpdesk/tree/18.0/helpdesk_mgmt_portal_follower>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
