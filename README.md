
[![Runboat](https://img.shields.io/badge/runboat-Try%20me-875A7B.png)](https://runboat.odoo-community.org/builds?repo=OCA/helpdesk&target_branch=18.0)
[![Pre-commit Status](https://github.com/OCA/helpdesk/actions/workflows/pre-commit.yml/badge.svg?branch=18.0)](https://github.com/OCA/helpdesk/actions/workflows/pre-commit.yml?query=branch%3A18.0)
[![Build Status](https://github.com/OCA/helpdesk/actions/workflows/test.yml/badge.svg?branch=18.0)](https://github.com/OCA/helpdesk/actions/workflows/test.yml?query=branch%3A18.0)
[![codecov](https://codecov.io/gh/OCA/helpdesk/branch/18.0/graph/badge.svg)](https://codecov.io/gh/OCA/helpdesk)
[![Translation Status](https://translation.odoo-community.org/widgets/helpdesk-18-0/-/svg-badge.svg)](https://translation.odoo-community.org/engage/helpdesk-18-0/?utm_source=widget)

<!-- /!\ do not modify above this line -->

# helpdesk

helpdesk

<!-- /!\ do not modify below this line -->

<!-- prettier-ignore-start -->

[//]: # (addons)

Available addons
----------------
addon | version | maintainers | summary
--- | --- | --- | ---
[helpdesk_mgmt](helpdesk_mgmt/) | 18.0.1.5.1 |  | Helpdesk
[helpdesk_mgmt_merge](helpdesk_mgmt_merge/) | 18.0.1.0.0 |  | Wizard to merge helpdesk tickets
[helpdesk_mgmt_portal_follower](helpdesk_mgmt_portal_follower/) | 18.0.1.0.0 | <a href='https://github.com/BernatObrador'><img src='https://github.com/BernatObrador.png' width='32' height='32' style='border-radius:50%;' alt='BernatObrador'/></a> <a href='https://github.com/ppyczko'><img src='https://github.com/ppyczko.png' width='32' height='32' style='border-radius:50%;' alt='ppyczko'/></a> <a href='https://github.com/mpascuall'><img src='https://github.com/mpascuall.png' width='32' height='32' style='border-radius:50%;' alt='mpascuall'/></a> | Add ticket followers from website portal
[helpdesk_mgmt_project](helpdesk_mgmt_project/) | 18.0.1.0.0 |  | Add the option to select project in the tickets.
[helpdesk_mgmt_rating](helpdesk_mgmt_rating/) | 18.0.1.0.0 |  | This module allows customer to rate the assistance received on a ticket.
[helpdesk_mgmt_sale](helpdesk_mgmt_sale/) | 18.0.1.0.0 |  | Add the option to select project in the sale orders.
[helpdesk_mgmt_timesheet](helpdesk_mgmt_timesheet/) | 18.0.1.0.0 |  | Add HR Timesheet to the tickets for Helpdesk Management.
[helpdesk_motive](helpdesk_motive/) | 18.0.1.0.0 | <a href='https://github.com/nelsonramirezs'><img src='https://github.com/nelsonramirezs.png' width='32' height='32' style='border-radius:50%;' alt='nelsonramirezs'/></a> <a href='https://github.com/max3903'><img src='https://github.com/max3903.png' width='32' height='32' style='border-radius:50%;' alt='max3903'/></a> | Keep the motive
[helpdesk_portal_restriction](helpdesk_portal_restriction/) | 18.0.1.0.0 | <a href='https://github.com/lbarry-apsl'><img src='https://github.com/lbarry-apsl.png' width='32' height='32' style='border-radius:50%;' alt='lbarry-apsl'/></a> | Helpdesk Portal Restriction
[helpdesk_product](helpdesk_product/) | 18.0.1.0.0 |  | Add the option to select product in the tickets.
[helpdesk_ticket_related](helpdesk_ticket_related/) | 18.0.1.0.0 | <a href='https://github.com/peluko00'><img src='https://github.com/peluko00.png' width='32' height='32' style='border-radius:50%;' alt='peluko00'/></a> | Link tickets to each other

[//]: # (end addons)

<!-- prettier-ignore-end -->

## Licenses

This repository is licensed under [AGPL-3.0](LICENSE).

However, each module can have a totally different license, as long as they adhere to Odoo Community Association (OCA)
policy. Consult each module's `__manifest__.py` file, which contains a `license` key
that explains its license.

----
OCA, or the [Odoo Community Association](http://odoo-community.org/), is a nonprofit
organization whose mission is to support the collaborative development of Odoo features
and promote its widespread use.
