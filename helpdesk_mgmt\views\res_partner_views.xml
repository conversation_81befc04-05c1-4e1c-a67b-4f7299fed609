<?xml version="1.0" ?>
<odoo>
    <record id="view_partner_form" model="ir.ui.view">
        <field name="name">view_partner_form</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form" />
        <field name="arch" type="xml">
            <div class="oe_button_box" name="button_box">
                <button
                    context="{'search_default_open': True, 'default_partner_id': id}"
                    name="action_view_helpdesk_tickets"
                    type="object"
                    class="oe_stat_button"
                    icon="fa-life-ring"
                    groups="helpdesk_mgmt.group_helpdesk_user_own"
                >
                    <field invisible="True" name="helpdesk_ticket_active_count" />
                    <field invisible="True" name="helpdesk_ticket_count" />
                    <field name="helpdesk_ticket_count_string" widget="statinfo" />
                </button>
            </div>
        </field>
    </record>
</odoo>
