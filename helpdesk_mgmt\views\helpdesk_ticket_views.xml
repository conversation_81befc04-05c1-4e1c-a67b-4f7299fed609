<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record
        id="action_helpdesk_ticket_kanban_from_dashboard"
        model="ir.actions.act_window"
    >
        <field name="name">Helpdesk Ticket</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">
            {'default_team_id': active_id}
        </field>
        <field name="domain">[('team_id', '=', active_id)]</field>
    </record>
    <record id="helpdesk_ticket_view_search" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.search</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <search>
                <field
                    string="Ticket"
                    name="number"
                    filter_domain="['|',('number', 'ilike', self),('name', 'ilike', self)]"
                />
                <field name="partner_id" />
                <field name="commercial_partner_id" />
                <field name="user_id" />
                <field name="name" />
                <field name="tag_ids" />
                <field name="stage_id" />
                <filter
                    string="Unassigned"
                    name="unassigned"
                    domain="[('user_id','=',False)]"
                />
                <filter string="Open" name="open" domain="[('closed', '=', False )]" />
                <filter
                    string="Unattended"
                    name="unattended"
                    domain="[('unattended', '=', True )]"
                />
                <filter
                    string="High Priority"
                    name="high_priority"
                    domain="[('priority','=','3')]"
                />
                <separator />
                <filter
                    string="Archived"
                    name="archived"
                    domain="[('active','=',False)]"
                />
                <separator />
                <filter
                    string="My Tickets"
                    name="mytickets"
                    domain="[('user_id','=',uid)]"
                />
                <filter
                    string="My Followed Tickets"
                    name="my_followed_tickets"
                    domain="[('message_is_follower', '=', True)]"
                />
                <separator />
                <filter
                    name="last_week"
                    string="Last Week"
                    domain="[('create_date','&gt;', (context_today() - datetime.timedelta(weeks=1)).strftime('%%Y-%%m-%%d') )]"
                />
                <separator />
                <filter
                    string="My Activities"
                    name="activities_my"
                    domain="[('activity_ids.user_id', '=', uid)]"
                />
                <filter
                    string="Late Activities"
                    name="activities_overdue"
                    domain="[('activity_ids.date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"
                />
                <filter
                    string="Today Activities"
                    name="activities_today"
                    domain="[('activity_ids.date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"
                />
                <filter
                    string="Future Activities"
                    name="activities_upcoming_all"
                    domain="[('activity_ids.date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))
                  ]"
                />
                <group expand="0" string="Group By">
                    <filter
                        string="Partner"
                        name="group_partner"
                        domain="[]"
                        context="{'group_by':'partner_id'}"
                    />
                    <filter
                        string="Commercial Entity"
                        name="group_commercial_partner"
                        domain="[]"
                        context="{'group_by':'commercial_partner_id'}"
                    />
                    <filter
                        string="Team"
                        name="group_team"
                        domain="[]"
                        context="{'group_by':'team_id'}"
                    />
                    <filter
                        string="User"
                        name="group_user"
                        domain="[]"
                        context="{'group_by':'user_id'}"
                    />
                    <filter
                        string="Stage"
                        name="group_stage"
                        domain="[]"
                        context="{'group_by':'stage_id'}"
                    />
                    <filter
                        string="Tag"
                        name="group_tag"
                        domain="[]"
                        context="{'group_by':'tag_ids'}"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="ticket_view_form" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.form</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <form string="Helpdesk Ticket">
                <header>
                    <button
                        string="Assign to me"
                        name="assign_to_me"
                        type="object"
                        invisible="user_id"
                    />
                    <field
                        name="stage_id"
                        widget="statusbar_duration"
                        options="{'clickable': '1', 'fold_field': 'fold'}"
                    />
                </header>
                <sheet>
                    <widget
                        name="web_ribbon"
                        title="Archived"
                        bg_color="bg-danger"
                        invisible="active"
                    />
                    <div class="oe_button_box" name="button_box" />
                    <div class="oe_title">
                        <h1 class="oe_title">
                            <field name="number" />
                        </h1>
                        <h2 class="o_row">
                            <field name="name" />
                        </h2>
                    </div>
                    <group name="main">
                        <group>
                            <field name="active" invisible="1" />
                            <field name="team_id" options='{"always_reload": True}' />
                            <field name="user_ids" invisible="1" readonly="1" />
                            <field
                                name="user_id"
                                options='{"always_reload": True}'
                                widget="many2one_avatar_user"
                            />
                            <field name="priority" widget="priority" />
                            <field
                                name="company_id"
                                options="{'no_create': True}"
                                groups="base.group_multi_company"
                            />
                            <field name="create_date" readonly="1" />
                            <field name="channel_id" />
                        </group>
                        <group>
                            <field name="partner_id" />
                            <field name="partner_name" />
                            <field name="partner_email" />
                            <field name="category_id" />
                            <field
                                name="tag_ids"
                                widget="many2many_tags"
                                options="{'no_create_edit': True, 'color_field': 'color',}"
                            />
                            <field name="sequence" groups="base.group_no_one" />
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" widget="html" />
                        </page>
                        <page string="Other Information" name="other_info">
                            <group>
                                <field name="last_stage_update" readonly="1" />
                                <field name="assigned_date" readonly="1" />
                                <field name="closed_date" readonly="1" />
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter />
            </form>
        </field>
    </record>
    <record id="ticket_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.list</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <list>
                <field name="priority" widget="priority" />
                <field name="sequence" widget="handle" />
                <field name="number" decoration-bf="1" />
                <field name="name" />
                <field name="partner_id" optional="hide" widget="many2one_avatar" />
                <field name="partner_name" optional="show" />
                <field name="user_id" optional="show" widget="many2one_avatar_user" />
                <field name="unattended" column_invisible="1" />
                <field name="closed" column_invisible="1" />
                <field
                    name="tag_ids"
                    widget="many2many_tags"
                    options="{'color_field': 'color'}"
                    optional="show"
                />
                <field
                    name="create_date"
                    widget="remaining_days"
                    readonly="1"
                    optional="show"
                />
                <field
                    name="last_stage_update"
                    widget="remaining_days"
                    optional="show"
                />
                <field name="activity_ids" widget="list_activity" optional="hide" />
                <field
                    name="stage_id"
                    widget="badge"
                    decoration-success="not unattended and not closed"
                    decoration-info="unattended == True"
                    decoration-muted="closed == True"
                />
            </list>
        </field>
    </record>
    <record id="view_helpdesk_ticket_kanban" model="ir.ui.view">
        <field name="name">helpdesk.ticket.kanban</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority" eval="100" />
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" default_group_by="stage_id">
                <field name="number" />
                <field name="active" />
                <field name="name" />
                <field name="partner_name" />
                <field name="user_id" />
                <field name="user_ids" />
                <field name="team_id" />
                <field name="sequence" />
                <field name="color" />
                <field name="stage_id" />
                <field name="priority" widget="priority" />
                <field name="assigned_date" />
                <progressbar
                    field="activity_state"
                    colors="{&quot;planned&quot;: &quot;success&quot;, &quot;today&quot;: &quot;warning&quot;, &quot;overdue&quot;: &quot;danger&quot;}"
                />
                <templates>
                    <t t-name="menu">
                        <t t-if="widget.editable">
                            <a
                                role="menuitem"
                                type="open"
                                class="dropdown-item"
                            >Edit</a>
                        </t>
                        <t t-if="widget.deletable">
                            <a
                                role="menuitem"
                                type="delete"
                                class="dropdown-item"
                            >Delete</a>
                        </t>
                        <field name="color" widget="kanban_color_picker" />
                    </t>
                    <t t-name="card">
                        <field class="fw-bold fs-5" name="name" />
                        <field name="category_id" class="text-muted" />
                        <field name="number" class="text-truncate" />
                        <small class="o_kanban_record_subtitle text-muted">
                            <field name="partner_id" />
                        </small>
                        <field
                            name="tag_ids"
                            widget="many2many_tags"
                            options="{'color_field': 'color'}"
                        />

                        <footer class="pt-1">
                            <div class="d-flex mt-auto align-items-center">
                                <field
                                    name="priority"
                                    widget="priority"
                                    groups="base.group_user"
                                    class="me-2"
                                />
                                <field name="activity_ids" widget="kanban_activity" />
                            </div>
                            <field name="kanban_state" widget="state_selection" />
                            <field
                                name="user_id"
                                widget="many2one_avatar_user"
                                class="ms-auto"
                            />
                        </footer>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="helpdesk_ticket_views_pivot" model="ir.ui.view">
        <field name="name">helpdesk.ticket.view.pivot</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <pivot string="Pivot Analysis" disable_linking="True">
                <field name="team_id" type="row" />
                <field name="assigned_date" type="col" />
            </pivot>
        </field>
    </record>
    <record id="action_duplicate_ticket" model="ir.actions.server">
        <field name="name">Duplicate</field>
        <field name="model_id" ref="model_helpdesk_ticket" />
        <field name="binding_model_id" ref="helpdesk_mgmt.model_helpdesk_ticket" />
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
            action = model.action_duplicate_tickets()
        </field>
    </record>
    <record id="model_helpdesk_ticket_action_share" model="ir.actions.server">
        <field name="name">Share</field>
        <field name="model_id" ref="helpdesk_mgmt.model_helpdesk_ticket" />
        <field name="binding_model_id" ref="helpdesk_mgmt.model_helpdesk_ticket" />
        <field name="binding_view_types">form</field>
        <field name="state">code</field>
        <field name="code">action = records.action_share()</field>
    </record>
</odoo>
