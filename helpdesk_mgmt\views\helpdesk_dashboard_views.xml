<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="helpdesk_ticket_kanban_view" model="ir.ui.view">
        <field name="name">helpdesk.ticket.team.kanban</field>
        <field name="model">helpdesk.ticket.team</field>
        <field name="arch" type="xml">
            <kanban class="oe_background_grey o_kanban_dashboard" create="0">
                <field name="name" />
                <field name="color" />
                <field name="todo_ticket_count" />
                <field name="todo_ticket_count_unassigned" />
                <field name="todo_ticket_count_unattended" />
                <field name="todo_ticket_count_high_priority" />
                <templates>
                    <t t-name="menu">
                        <div class="o_kanban_card_manage_section">
                            <div role="menuitem" class="o_kanban_manage_reports">
                                <div class="o_kanban_card_manage_title ps-4 pb-1">
                                    <span class="fw-bolder">View</span>
                                </div>
                                <a
                                    role="menuitem"
                                    class="dropdown-item"
                                    name="%(action_helpdesk_ticket_kanban_from_dashboard)d"
                                    type="action"
                                > All
                                </a>
                                <div role="separator" class="dropdown-divider" />
                                <ul
                                    t-if="widget.editable"
                                    class="oe_kanban_colorpicker"
                                    data-field="color"
                                />
                            </div>
                        </div>
                    </t>
                    <t t-name="card">
                        <div t-attf-class="#{kanban_color(record.color.raw_value)}">
                            <div t-attf-class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <a
                                            name="%(action_helpdesk_ticket_kanban_from_dashboard)d"
                                            type="action"
                                        >
                                            <field name="name" />
                                        </a>
                                    </div>
                                </div>
                                <div class="container o_kanban_card_content">
                                    <div class="row">
                                        <div
                                            class="col-6 o_kanban_primary_left"
                                            style="padding-top:20px;"
                                        >
                                            <button
                                                class="btn btn-primary"
                                                name="%(action_helpdesk_ticket_kanban_from_dashboard)d"
                                                type="action"
                                                context="{'search_default_open': 1}"
                                            >
                                                <t
                                                t-esc="record.todo_ticket_count.value"
                                            />
                                                To Do
                                            </button>
                                        </div>
                                        <div class="col-6 o_kanban_primary_right">
                                            <div class="row">
                                                <div class="col-9">
                                                    <a
                                                        name="%(action_helpdesk_ticket_kanban_from_dashboard)d"
                                                        type="action"
                                                        context="{'search_default_unassigned': 1}"
                                                    >
                                                        Unassigned
                                                    </a>
                                                </div>
                                                <div class="col-3">
                                                    <t
                                                        t-esc="record.todo_ticket_count_unassigned.value"
                                                    />
                                                </div>
                                                <div class="col-9">
                                                    <a
                                                        name="%(action_helpdesk_ticket_kanban_from_dashboard)d"
                                                        type="action"
                                                        context="{'search_default_unattended': 1}"
                                                    >
                                                        Unattended
                                                    </a>
                                                </div>
                                                <div class="col-3">
                                                    <t
                                                        t-esc="record.todo_ticket_count_unattended.value"
                                                    />
                                                </div>
                                                <div class="col-9">
                                                    <a
                                                        name="%(action_helpdesk_ticket_kanban_from_dashboard)d"
                                                        type="action"
                                                        context="{'search_default_high_priority': 1}"
                                                    >
                                                        High Priority
                                                    </a>
                                                </div>
                                                <div class="col-3">
                                                    <t
                                                        t-esc="record.todo_ticket_count_high_priority.value"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
</odoo>
