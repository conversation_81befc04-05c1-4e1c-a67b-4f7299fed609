<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="edit_project" model="ir.ui.view">
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.edit_project" />
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button
                    class="oe_stat_button"
                    type="action"
                    icon="fa-file"
                    name="%(ticket_action_from_project)d"
                    context="{'search_default_project_id': id, 'default_project_id': id}"
                    groups="helpdesk_mgmt.group_helpdesk_user_own"
                >
                    <field string="Tickets" name="ticket_count" widget="statinfo" />
                </button>
            </xpath>
            <field name="label_tasks" position="after">
                <field name="label_tickets" />
            </field>
        </field>
    </record>
    <record id="view_project_kanban" model="ir.ui.view">
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.view_project_kanban" />
        <field name="arch" type="xml">
            <xpath expr="//a[hasclass('o_project_kanban_box')]" position="after">
                <a
                    class="o_project_kanban_box"
                    name="%(ticket_action_from_project)d"
                    type="action"
                    context="{'search_default_project_id': id, 'default_project_id': id}"
                    groups="helpdesk_mgmt.group_helpdesk_user_own"
                >
                    <div>
                        <field name="ticket_count" class="o_value" />
                        <field name="label_tickets" class="ms-1" />
                    </div>
                </a>
            </xpath>
        </field>
    </record>
    <record id="view_project_search" model="ir.ui.view">
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.view_project_project_filter" />
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='inactive']" position="after">
                <separator />
                <filter
                    string="Open Tickets"
                    name="open_tickets"
                    domain="[('todo_ticket_count', '&gt;', 0)]"
                    groups="helpdesk_mgmt.group_helpdesk_user_own"
                />
            </xpath>
        </field>
    </record>
</odoo>
