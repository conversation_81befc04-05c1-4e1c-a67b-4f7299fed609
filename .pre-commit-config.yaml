exclude: |
  (?x)
  # NOT INSTALLABLE ADDONS
  # END NOT INSTALLABLE ADDONS
  # Files and folders generated by bots, to avoid loops
  ^setup/|/static/description/index\.html$|
  # We don't want to mess with tool-generated files
  .svg$|/tests/([^/]+/)?cassettes/|^.copier-answers.yml$|^.github/|^eslint.config.cjs|^prettier.config.cjs|
  # Maybe reactivate this when all README files include prettier ignore tags?
  ^README\.md$|
  # Library files can have extraneous formatting (even minimized)
  /static/(src/)?lib/|
  # Repos using Sphinx to generate docs don't need prettying
  ^docs/_templates/.*\.html$|
  # Don't bother non-technical authors with formatting issues in docs
  readme/.*\.(rst|md)$|
  # Ignore build and dist directories in addons
  /build/|/dist/|
  # Ignore test files in addons
  /tests/samples/.*|
  # You don't usually want a bot to modify your legal texts
  (LICENSE.*|COPYING.*)
default_language_version:
  python: python3
  node: "22.9.0"
repos:
  - repo: local
    hooks:
      # These files are most likely copier diff rejection junks; if found,
      # review them manually, fix the problem (if needed) and remove them
      - id: forbidden-files
        name: forbidden files
        entry: found forbidden files; remove them
        language: fail
        files: "\\.rej$"
      - id: en-po-files
        name: en.po files cannot exist
        entry: found a en.po file
        language: fail
        files: '[a-zA-Z0-9_]*/i18n/en\.po$'
  - repo: https://github.com/sbidoul/whool
    rev: v1.2
    hooks:
      - id: whool-init
  - repo: https://github.com/oca/maintainer-tools
    rev: bf9ecb9938b6a5deca0ff3d870fbd3f33341fded
    hooks:
      # update the NOT INSTALLABLE ADDONS section above
      - id: oca-update-pre-commit-excluded-addons
      - id: oca-fix-manifest-website
        args: ["https://github.com/OCA/helpdesk"]
      - id: oca-gen-addon-readme
        args:
          - --addons-dir=.
          - --branch=18.0
          - --org-name=OCA
          - --repo-name=helpdesk
          - --if-source-changed
          - --keep-source-digest
          - --convert-fragments-to-markdown
      - id: oca-gen-external-dependencies
  - repo: https://github.com/OCA/odoo-pre-commit-hooks
    rev: v0.0.33
    hooks:
      - id: oca-checks-odoo-module
      - id: oca-checks-po
        args:
          - --disable=po-pretty-format
  - repo: local
    hooks:
      - id: prettier
        name: prettier (with plugin-xml)
        entry: prettier
        args:
          - --write
          - --list-different
          - --ignore-unknown
        types: [text]
        files: \.(css|htm|html|js|json|jsx|less|md|scss|toml|ts|xml|yaml|yml)$
        language: node
        additional_dependencies:
          - "prettier@3.3.3"
          - "@prettier/plugin-xml@3.4.1"
  - repo: local
    hooks:
      - id: eslint
        name: eslint
        entry: eslint
        args:
          - --color
          - --fix
        verbose: true
        types: [javascript]
        language: node
        additional_dependencies:
          - "eslint@9.12.0"
          - "eslint-plugin-jsdoc@50.3.1"
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        # exclude autogenerated files
        exclude: /README\.rst$|\.pot?$
      - id: end-of-file-fixer
        # exclude autogenerated files
        exclude: /README\.rst$|\.pot?$
      - id: debug-statements
      - id: fix-encoding-pragma
        args: ["--remove"]
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-merge-conflict
        # exclude files where underlines are not distinguishable from merge conflicts
        exclude: /README\.rst$|^docs/.*\.rst$
      - id: check-symlinks
      - id: check-xml
      - id: mixed-line-ending
        args: ["--fix=lf"]
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.8
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      - id: ruff-format
  - repo: https://github.com/OCA/pylint-odoo
    rev: v9.1.3
    hooks:
      - id: pylint_odoo
        name: pylint with optional checks
        args:
          - --rcfile=.pylintrc
          - --exit-zero
        verbose: true
      - id: pylint_odoo
        args:
          - --rcfile=.pylintrc-mandatory
