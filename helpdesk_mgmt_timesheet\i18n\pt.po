# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt_timesheet
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2024-02-29 20:34+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
msgid ""
"<span class=\"o_label\">\n"
"                            <i class=\"fa fa-lg fa-play-circle text-"
"success\"/>\n"
"                            Start work\n"
"                        </span>"
msgstr ""

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
msgid ""
"<span class=\"o_label\">\n"
"                            <i class=\"fa fa-lg fa-stop-circle text-"
"warning\"/>\n"
"                            Stop work\n"
"                        </span>"
msgstr ""

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__allow_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket_team__allow_timesheet
msgid "Allow Timesheet"
msgstr "Permitir Registo de Tempos"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linha Analítica"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Date"
msgstr "Data"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Duration (Hour(s))"
msgstr "Duração (Hora(s))"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.ticket_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "End work"
msgstr "Acabar trabalho"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket de Helpdesk"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_helpdesk_ticket_team
msgid "Helpdesk Ticket Team"
msgstr "Equipa de Ticket de Helpdesk"

#. module: helpdesk_mgmt_timesheet
#: model:project.project,name:helpdesk_mgmt_timesheet.project_1
msgid "Helpdesk general project"
msgstr "Projeto geral do Helpdesk"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_hr_timesheet_switch
msgid "Helper to quickly switch between timesheet lines"
msgstr ""

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,help:helpdesk_mgmt_timesheet.field_helpdesk_ticket__show_time_control
msgid "Indicate which time control button to show, if any."
msgstr "Indique qual botão de controle de tempo mostrar, ou nenhum."

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__last_timesheet_activity
msgid "Last Timesheet Activity"
msgstr "Última Atividade do Registo de Tempos"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_view_search
msgid "Late"
msgstr "Atrasado"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__planned_hours
msgid "Planned Hours"
msgstr "Horas Planeadas"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__progress
msgid "Progress"
msgstr "Progresso"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__remaining_hours
msgid "Remaining Hours"
msgstr "Horas Restantes"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Resume work"
msgstr "Retomar o trabalho"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__show_time_control
msgid "Show Time Control"
msgstr "Mostrar Controlo de Tempo"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.ticket_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Start work"
msgstr "Iniciar Trabalho"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.ticket_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Stop work"
msgstr "Parar trabalho"

#. module: helpdesk_mgmt_timesheet
#: model:project.project,label_tasks:helpdesk_mgmt_timesheet.project_1
msgid "Tasks"
msgstr "Tarefas"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_account_analytic_line__ticket_id
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_timesheets_analysis_report__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_table
msgid "Ticket"
msgstr "Ticket"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_timesheets_analysis_report__ticket_partner_id
msgid "Ticket Partner"
msgstr ""

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_account_analytic_line__ticket_partner_id
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.hr_timesheet_line_search
msgid "Ticket partner"
msgstr "Parceiro do ticket"

#. module: helpdesk_mgmt_timesheet
#: model:project.project,label_tickets:helpdesk_mgmt_timesheet.project_1
#: model:project.task,label_tickets:helpdesk_mgmt_timesheet.project_task_1
msgid "Tickets"
msgstr "Tickets"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__timesheet_ids
msgid "Timesheet"
msgstr "Registo de Tempos"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_view_search
msgid "Timesheet allowed"
msgstr "Registo de tempos permitido"

#. module: helpdesk_mgmt_timesheet
#: model:ir.actions.act_window,name:helpdesk_mgmt_timesheet.helpdesk_timesheet_action
#: model:ir.ui.menu,name:helpdesk_mgmt_timesheet.helpdesk_timesheet_menu
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Timesheets"
msgstr "Registos de Tempos"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr ""

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__total_hours
msgid "Total Hours"
msgstr "Total de Horas"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_view_search
msgid "With activity today"
msgstr "Com atividade hoje"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.hr_timesheet_line_search
msgid "With ticket"
msgstr "Com ticket"

#~ msgid "Change the Default Project will not have retroactive effects."
#~ msgstr "Alterar o Projeto Padrão não terá efeitos retroativos."
