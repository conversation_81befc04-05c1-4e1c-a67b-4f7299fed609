.. image:: https://odoo-community.org/readme-banner-image
   :target: https://odoo-community.org/get-involved?utm_source=readme
   :alt: Odoo Community Association

===================
Helpdesk Management
===================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:24cf695efcc790fba66f7516dd4b55a461932ff69979761489a505a5af0abdef
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Production%2FStable-green.png
    :target: https://odoo-community.org/page/development-status
    :alt: Production/Stable
.. |badge2| image:: https://img.shields.io/badge/license-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fhelpdesk-lightgray.png?logo=github
    :target: https://github.com/OCA/helpdesk/tree/18.0/helpdesk_mgmt
    :alt: OCA/helpdesk
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/helpdesk-18-0/helpdesk-18-0-helpdesk_mgmt
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/helpdesk&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module adds Helpdesk functionality in Odoo.

**Table of contents**

.. contents::
   :local:

Configuration
=============

To configure this module, you need to:

1. Edit or create new channels.
2. Edit or create new categories.
3. Edit or create new stages.
4. Edit or create new teams.
5. Edit or create new tags.

Channels
--------

1. Go to *Helpdesk > Configuration > Channels* to edit or create new
   channels.
2. Edit or create a channel.
3. Set the name for the channel.
4. You can also Activate or Deactivate channels.

|image1|

Categories
----------

1. Go to *Helpdesk > Configuration > Categories* to edit or create new
   categories.
2. Edit or create a new category.
3. Set the name for the category.
4. You can also Activate or Deactivate categories.

|image2|

Stages
------

1. Go to *Helpdesk > Configuration > Stages* to edit or create new
   stages.
2. Edit or create a new stage.
3. Set the name for the stage.
4. Set the sequence order for the stage.
5. You can select an Email template.
6. Mark the Unattended checkbox if the stage contains unattended
   tickets.
7. Mark the Closed checkbox if the stage contains closed tickets.
8. You can add a description for the stage.
9. You can also Activate or Deactivate stages.

|image3|

You can also sort the stage sequence if you move up or down the stages
in the list view.

Teams
-----

1. Go to *Helpdesk > Configuration > Teams* to edit or create new teams.
2. Edit or create a new team.
3. Set the name for the team.
4. Add the teams members.
5. You can also Activate or Deactivate teams.

|image4|

Tags
----

1. Go to *Helpdesk > Configuration > Ticket Tags* to edit or create new
   tags.
2. Edit or create a new tag.
3. Set the name for the tag.
4. Set the color index for the tag.
5. You can also Activate or Deactivate tags.

|image5|

Permissions
-----------

There are restrictions to read tickets according to the user's
permissions set in Helpdesk.

1. *User: Personal tickets*: User is able to see their tickets (those
   that are assigned to their user) or those that are no team nor user
   is assigned.
2. *User: Team tickets*: User is able to see all the tickets that are
   assigned to the teams to which he/she belongs or the tickets that are
   not assigned to any team nor user.
3. *User*: User is able to see all the tickets.

.. |image1| image:: https://raw.githubusercontent.com/OCA/helpdesk/18.0/helpdesk_mgmt/static/description/Channels.PNG
.. |image2| image:: https://raw.githubusercontent.com/OCA/helpdesk/18.0/helpdesk_mgmt/static/description/Categories.PNG
.. |image3| image:: https://raw.githubusercontent.com/OCA/helpdesk/18.0/helpdesk_mgmt/static/description/Stages.PNG
.. |image4| image:: https://raw.githubusercontent.com/OCA/helpdesk/18.0/helpdesk_mgmt/static/description/Teams.PNG
.. |image5| image:: https://raw.githubusercontent.com/OCA/helpdesk/18.0/helpdesk_mgmt/static/description/Tags.PNG

Usage
=====

1. Go to *Helpdesk* or *Helpdesk > Dashboard* to see the tickets
   dashboard
2. In the Kanban view, click in the kanban card of a team to see their
   tickets and create new ones.

|Tickets_Kanban|

To create a new ticket from the kanban view:

1. Press *Create* button or click on the plus icon at the top of the
   column of a stage.
2. Set the name or subject for the ticket.
3. Select the team that will manage the ticket.
4. You can select a user to assign the ticket.
5. Set the priority of the ticket.
6. Select the partner, and you can also set the partner name and email.
7. You can select a category and set tags for the ticket.
8. Add a description.
9. You can also attach files to the ticket.

|Tickets01| |Tickets02|

.. |Tickets_Kanban| image:: https://raw.githubusercontent.com/OCA/helpdesk/18.0/helpdesk_mgmt/static/description/Tickets_Kanban.PNG
.. |Tickets01| image:: https://raw.githubusercontent.com/OCA/helpdesk/18.0/helpdesk_mgmt/static/description/Tickets01.PNG
.. |Tickets02| image:: https://raw.githubusercontent.com/OCA/helpdesk/18.0/helpdesk_mgmt/static/description/Tickets02.PNG

Known issues / Roadmap
======================

- Add a tour feature similar to what the ``project`` module defines to
  discover projects / tasks.
- Update portal tests defined in ``tests/test_portal.py`` to rely on
  tour specs (in JS) in order to replicate the navigation behavior of
  portal users.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/helpdesk/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/helpdesk/issues/new?body=module:%20helpdesk_mgmt%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* AdaptiveCity
* Tecnativa
* ForgeFlow
* C2i Change 2 Improve
* Domatix
* Factor Libre
* SDi Soluciones

Contributors
------------

- `Domatix <https://www.domatix.com>`__:

  - Carlos Martínez
  - Catalin Airimitoaie
  - Álvaro López
  - Samuel Calvo

- `Adaptive City <https://www.adaptivecity.com>`__:

  - Aitor Bouzas

- `SDi Soluciones, S.L. <https://www.sdi.es>`__:

  - Oscar Soto
  - Jorge Luis Quinteros

- `C2i Change 2 improve <http://www.c2i.es>`__:

  - Eduardo Magdalena <<EMAIL>>

- `Factor Libre <https://factorlibre.com>`__:

  - María Alhambra
  - Daniel Cano

- `Tecnativa <https://www.tecnativa.com>`__:

  - Pedro M. Baeza
  - Víctor Martínez
  - Carolina Fernandez
  - Carlos Roca

- `ID42 Sistemas <https://www.id42.com.br>`__:

  - Marcel Savegnago
  - Eduardo Aparício

- `Obertix <https://www.obertix.net>`__:

  - Vicent Cubells

- `Solvos <https://www.solvos.es>`__:

  - David Alonso
  - Dante Pereyra

- `XCG Consulting <https://xcg-consulting.fr>`__:

  - Houzéfa Abbasbhay

- `Kencove <https://kencove.com>`__:

  - Mohamed Alkobrosli

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/helpdesk <https://github.com/OCA/helpdesk/tree/18.0/helpdesk_mgmt>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
