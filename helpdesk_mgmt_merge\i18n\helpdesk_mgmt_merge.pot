# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt_merge
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: helpdesk_mgmt_merge
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_merge.helpdesk_ticket_merge_view_form
msgid ""
"<span class=\"text-muted\">NB: This will archive the selected tickets (Except the destination ticket)\n"
"                    </span>"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__user_id
msgid "Assigned to"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_merge.helpdesk_ticket_merge_view_form
msgid "Cancel"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__create_new_ticket
msgid "Create a new ticket"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__create_uid
msgid "Created by"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__create_date
msgid "Created on"
msgstr ""

#. module: helpdesk_mgmt_merge
#. odoo-python
#: code:addons/helpdesk_mgmt_merge/wizard/helpdesk_ticket_merge.py:0
msgid "Description from ticket %(name)s: %(description)s"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__dst_helpdesk_team_id
msgid "Destination Helpdesk Team"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__display_name
msgid "Display Name"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model,name:helpdesk_mgmt_merge.model_helpdesk_ticket_merge
msgid "Helpdesk Ticket Merge"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__id
msgid "ID"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__write_uid
msgid "Last Updated by"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__write_date
msgid "Last Updated on"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.actions.act_window,name:helpdesk_mgmt_merge.action_helpdesk_ticket_merge
msgid "Merge Helpdesk Tickets"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_merge.helpdesk_ticket_merge_view_form
msgid "Merge Tickets"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__dst_ticket_id
msgid "Merge into an existing ticket"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__dst_ticket_name
msgid "New ticket name"
msgstr ""

#. module: helpdesk_mgmt_merge
#. odoo-python
#: code:addons/helpdesk_mgmt_merge/wizard/helpdesk_ticket_merge.py:0
msgid "No description"
msgstr ""

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__ticket_ids
msgid "Tickets to Merge"
msgstr ""
