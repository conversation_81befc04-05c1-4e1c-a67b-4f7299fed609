<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_helpdesk_ticket_tag_search" model="ir.ui.view">
        <field name="name">helpdesk.ticket.tag.search</field>
        <field name="model">helpdesk.ticket.tag</field>
        <field name="arch" type="xml">
            <search string="Ticket Tag Search">
                <filter
                    string="Archived"
                    name="inactive"
                    domain="[('active','=',False)]"
                />
                <separator />
                <field name="name" filter_domain="[('name', 'ilike', self)]" />
            </search>
        </field>
    </record>
    <record id="view_helpdesk_ticket_tag_form" model="ir.ui.view">
        <field name="name">helpdesk.ticket.tag.form</field>
        <field name="model">helpdesk.ticket.tag</field>
        <field name="arch" type="xml">
            <form string="Tags">
                <header />
                <sheet>
                    <field name="active" invisible="1" />
                    <div class="oe_button_box" name="button_box" />
                    <widget
                        name="web_ribbon"
                        title="Archived"
                        bg_color="bg-danger"
                        invisible="active"
                    />
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only" />
                        <h1>
                            <field name="name" required="1" />
                        </h1>
                    </div>
                    <group name="main">
                        <field name="color" widget="color_picker" />
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_helpdesk_ticket_tag_tree" model="ir.ui.view">
        <field name="name">helpdesk.ticket.tag.list</field>
        <field name="model">helpdesk.ticket.tag</field>
        <field name="arch" type="xml">
            <list decoration-muted="not active" editable="bottom">
                <field name="name" />
                <field name="color" widget="color_picker" />
                <field name="active" column_invisible="1" />
            </list>
        </field>
    </record>
</odoo>
