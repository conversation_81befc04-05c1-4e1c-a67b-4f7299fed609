<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_helpdesk_ticket_team_search" model="ir.ui.view">
        <field name="name">helpdesk.ticket.team.search</field>
        <field name="model">helpdesk.ticket.team</field>
        <field name="arch" type="xml">
            <search string="Helpdesk Team Search">
                <filter
                    string="Archived"
                    name="inactive"
                    domain="[('active','=',False)]"
                />
                <separator />
                <field name="name" filter_domain="[('name', 'ilike', self)]" />
                <field
                    name="user_ids"
                    string="User"
                    filter_domain="[('user_ids', 'ilike', self)]"
                />
                <field name="company_id" groups="base.group_multi_company" />
                <group>
                    <filter
                        string="Company"
                        name="company"
                        context="{'group_by': 'company_id'}"
                        groups="base.group_multi_company"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="view_helpdesk_team_form" model="ir.ui.view">
        <field name="name">view.helpdesk_team.form</field>
        <field name="model">helpdesk.ticket.team</field>
        <field name="arch" type="xml">
            <form string="Helpdesk Team">
                <sheet>
                    <div class="oe_button_box" name="button_box" />
                    <widget
                        name="web_ribbon"
                        title="Archived"
                        bg_color="bg-danger"
                        invisible="active"
                    />
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only" string="Helpdesk Team" />
                        <h1>
                            <field name="name" placeholder="Helpdesk Team name..." />
                        </h1>
                        <div name="options_active" />
                    </div>
                    <group>
                        <group name="left">
                            <field name="active" invisible="1" />
                            <field name="user_id" domain="[('share', '=', False)]" />
                            <label for="alias_name" string="Email Alias" />
                            <div class="oe_inline" name="alias_def">
                                <field
                                    name="alias_id"
                                    class="oe_read_only oe_inline"
                                    string="Email Alias"
                                    required="0"
                                />
                                <div
                                    class="oe_edit_only oe_inline"
                                    name="edit_alias"
                                    style="display: inline;"
                                >
                                    <field name="alias_name" class="oe_inline" />
@                                    <field
                                    name="alias_domain"
                                    class="oe_inline"
                                    readonly="1"
                                />
                                    <button
                                    icon="fa-arrow-right"
                                    type="action"
                                    name="%(base_setup.action_general_configuration)d"
                                    string="Configure domain name"
                                    class="btn-link"
                                    invisible="alias_domain"
                                />
                                </div>
                            </div>
                            <field
                                name="alias_contact"
                                class="oe_inline"
                                string="Accept Emails From"
                            />
                            <field
                                name="company_id"
                                options="{'no_create': True}"
                                groups="base.group_multi_company"
                            />
                            <field name="show_in_portal" />
                        </group>
                        <group name="right" />
                    </group>
                    <notebook>
                        <page name="members" string="Team Members">
                            <field
                                name="user_ids"
                                widget="many2many"
                                options="{'not_delete': True}"
                            >
                                <kanban
                                    quick_create="false"
                                    create="true"
                                    delete="true"
                                >
                                    <field name="id" />
                                    <field name="name" />
                                    <templates>
                                        <t t-name="card" class="flex-row">
                                            <aside class="o_kanban_aside_full">
                                                <field
                                                    name="avatar_128"
                                                    class="o_kanban_image_fill w-100"
                                                    widget="image"
                                                    options="{'img_class': 'object-fit-cover'}"
                                                    alt="Avatar"
                                                />
                                            </aside>

                                            <main class="ps-2 ps-md-0">
                                                <field name="name" class="fw-bold" />
                                            </main>
                                        </t>
                                    </templates>
                                </kanban>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter />
            </form>
        </field>
    </record>
    <record id="view_helpdesk_team_tree" model="ir.ui.view">
        <field name="name">view.helpdesk_team.list</field>
        <field name="model">helpdesk.ticket.team</field>
        <field name="arch" type="xml">
            <list>
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="category_ids" widget="many2many_tags" />
                <field name="todo_ticket_count" string="Active tickets" />
                <field
                    name="todo_ticket_count_unassigned"
                    string="Unassigned tickets"
                />
                <field
                    name="todo_ticket_count_unattended"
                    string="Unattended tickets"
                />
                <field
                    name="todo_ticket_count_high_priority"
                    string="Priority tickets"
                />
                <field
                    name="company_id"
                    optional="hide"
                    groups="base.group_multi_company"
                />
            </list>
        </field>
    </record>
</odoo>
