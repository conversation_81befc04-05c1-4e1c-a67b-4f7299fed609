<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <!-- VIEW PRODUCT TEMPLATE-->
    <record id="product_view_template" model="ir.ui.view">
        <field name="name">product.template.common.form</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view" />
        <field name="arch" type="xml">
            <field name="product_tooltip" position="before">
                <field name="ticket_active" />
            </field>
        </field>
    </record>

    <record id="product_template_search_view_helpdesk" model="ir.ui.view">
        <field name="name">product.template.search</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view" />
        <field name="arch" type="xml">
            <search>
                <filter
                    string="Available for Helpdesk"
                    name="helpdesk_active"
                    domain="[('ticket_active', '=', True )]"
                />
            </search>
        </field>
    </record>

    <!-- ACTION WINDOW FOR PRODUCT IN HELPDESK -->
    <record id="product_template_action_helpdesk" model="ir.actions.act_window">
        <field name="name">Products</field>
        <field name="res_model">product.template</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">kanban,list,form,activity</field>
        <field name="search_view_id" ref="product_template_search_view_helpdesk" />
        <field name="context">{'search_default_helpdesk_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new product
            </p>
            <p> You currently have no products available for Helpdesk Tickets. <br
            /> A product can
                be either a physical product or a service that you sell to your customers. </p>
        </field>
    </record>
</odoo>
