# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt_rating
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-06-30 09:25+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: none\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.10.4\n"

#. module: helpdesk_mgmt_rating
#: model:mail.template,body_html:helpdesk_mgmt_rating.rating_ticket_email_template
msgid ""
"<div>\n"
"                <t t-set=\"access_token\" t-value=\"object."
"_rating_get_access_token()\"/>\n"
"                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                                <p>Hello,</p>\n"
"                                <p>Please take a moment to rate our services "
"related to the ticket \"<strong><t t-out=\"object.number\"/> - <t t-"
"out=\"object.name\"/></strong>\"assigned to <strong>\n"
"                                    <t t-out=\"object._rating_get_operator()."
"name\"/>\n"
"                                </strong>.</p>\n"
"                                <p>We appreciate your feedback. It helps us "
"to improve continuously.</p>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <td style=\"padding:10px 20px\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" "
"style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                                    <tr>\n"
"                                        <td style=\"text-align:center;\">\n"
"                                            <h2 style=\"font-weight:300;font-"
"size:18px;\">Tell us how you feel about our service:</h2>\n"
"                                            <div style=\"text-color: "
"#888888\">(click on one of these smileys)</div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td style=\"padding:10px 10px;\">\n"
"                                            <table style=\"width:100%;text-"
"align:center;margin-top:2rem;\">\n"
"                                                <tr>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/5\">\n"
"                                                            <img "
"alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" "
"title=\"Satisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/3\">\n"
"                                                            <img alt=\"Not "
"satisfied\" src=\"/rating/static/src/img/rating_3.png\" title=\"Not "
"satisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/1\">\n"
"                                                            <img "
"alt=\"Highly Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" "
"title=\"Highly Dissatisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:auto;text-align:center;"
"font-size:12px;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td style=\"padding-top:10px;color:#afafaf;\">\n"
"                                <t t-if=\"object.rating_status == "
"'stage_change'\">\n"
"                                    <p>This customer survey has been sent "
"because your ticket has been moved to the stage <b t-out=\"object.stage_id."
"name\"/></p>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""
"<div>\n"
"\n"
"                <t t-set=\"access_token\" t-value="
"\"object._rating_get_access_token()\"></t>\n"
"\n"
"                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"\n"
"                    <tbody>\n"
"\n"
"                        <tr>\n"
"\n"
"                            <td style=\"padding:15px 20px 10px 20px;\">\n"
"\n"
"                                <p>Salve,</p>\n"
"\n"
"                                <p>chiediamo la cortesia di dedicare qualche "
"momento per valutare il nostro servizio relativo alla richiesta \"<strong>\n"
"\n"
"                                <t t-out=\"object.number\"></t> - <t t-out="
"\"object.name\"></t> \n"
"\n"
"                                </strong>\"assegnata a <strong>\n"
"\n"
"                                <t t-out="
"\"object._rating_get_operator().name\"></t></strong>.</p>\n"
"\n"
"                                <p>Apprezziamo la sua valutazione. Ci aiuta "
"a migliorare continuamente.</p>\n"
"\n"
"                            </td>\n"
"\n"
"                        </tr>\n"
"\n"
"                        <tr>\n"
"\n"
"                            <td style=\"padding:10px 20px\">\n"
"\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style="
"\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"\n"
"                                    <tr>\n"
"\n"
"                                        <td style=\"text-align:center;\">\n"
"\n"
"                                            <h2 style=\"font-weight:300;font-"
"size:18px;\">Tell us how you feel about our service:</h2>\n"
"\n"
"                                            <div style=\"text-color: #"
"888888\">(fare clic su una di questi smile)</div>\n"
"\n"
"                                        </td>\n"
"\n"
"                                    </tr>\n"
"\n"
"                                    <tr>\n"
"\n"
"                                        <td style=\"padding:10px 10px;\">\n"
"\n"
"                                            <table style="
"\"width:100%;text-align:center;margin-top:2rem;\">\n"
"\n"
"                                                <tr>\n"
"\n"
"                                                    <td>\n"
"\n"
"                                                        <a t-attf-href="
"\"/rate/{{ access_token }}/5\">\n"
"\n"
"                                                            <img alt="
"\"Soddisfatto\" src=\"/rating/static/src/img/rating_5.png\" title="
"\"Soddisfatto\"/>\n"
"\n"
"                                                        </a>\n"
"\n"
"                                                    </td>\n"
"\n"
"                                                    <td>\n"
"\n"
"                                                        <a t-attf-href="
"\"/rate/{{ access_token }}/3\">\n"
"\n"
"                                                            <img alt="
"\"Non soddisfatto\" src=\"/rating/static/src/img/rating_3.png\" title="
"\"Non soddisfatto\"/>\n"
"\n"
"                                                        </a>\n"
"\n"
"                                                    </td>\n"
"\n"
"                                                    <td>\n"
"\n"
"                                                        <a t-attf-href="
"\"/rate/{{ access_token }}/1\">\n"
"\n"
"                                                            <img alt="
"\"Molto inoddisfatto\" src=\"/rating/static/src/img/rating_1.png\" title="
"\"Molto inoddisfatto\"/>\n"
"\n"
"                                                        </a>\n"
"\n"
"                                                    </td>\n"
"\n"
"                                                </tr>\n"
"\n"
"                                            </table>\n"
"\n"
"                                        </td>\n"
"\n"
"                                    </tr>\n"
"\n"
"                                </table>\n"
"\n"
"                            </td>\n"
"\n"
"                        </tr>\n"
"\n"
"                    </tbody>\n"
"\n"
"                </table>\n"
"\n"
"                <table style="
"\"width:600px;margin:auto;text-align:center;font-size:12px;\">\n"
"\n"
"                    <tbody>\n"
"\n"
"                        <tr>\n"
"\n"
"                            <td style=\"padding-top:10px;color:#afafaf;\">\n"
"\n"
"                                <t t-if="
"\"object.rating_status == 'stage_change'\">\n"
"\n"
"                                    <p>Questo sondaggio cliente è stato "
"inviato perché la sua richiesta è passata allo stato <b t-out="
"\"object.stage_id.name\"></b></p>\n"
"\n"
"                                </t>\n"
"\n"
"                            </td>\n"
"\n"
"                        </tr>\n"
"\n"
"                    </tbody>\n"
"\n"
"                </table>\n"
"\n"
"            </div>\n"
"        "

#. module: helpdesk_mgmt_rating
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.view_helpdesk_ticket_kanban_inherit
msgid ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"
msgstr ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentuale di "
"soddisfazione\" title=\"Percentuale di soddisfazione\"/>"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr "Conteggio allegati"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_avg
msgid "Average Rating"
msgstr "Valutazione media"

#. module: helpdesk_mgmt_rating
#: model:ir.actions.act_window,name:helpdesk_mgmt_rating.all_helpdesk_ticket_rating_action
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_status
#: model:ir.ui.menu,name:helpdesk_mgmt_rating.helpdesk_ticket_rating_menu
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.view_helpdesk_ticket_kanban_inherit
msgid "Customer Rating"
msgstr "Valutazione Cliente"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__has_message
msgid "Has Message"
msgstr "Ha un messaggio"

#. module: helpdesk_mgmt_rating
#: model:ir.model,name:helpdesk_mgmt_rating.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket assistenza clienti"

#. module: helpdesk_mgmt_rating
#: model:ir.model,name:helpdesk_mgmt_rating.model_helpdesk_ticket_stage
msgid "Helpdesk Ticket Stage"
msgstr "Fase ticket assistenza clienti"

#. module: helpdesk_mgmt_rating
#: model:mail.template,name:helpdesk_mgmt_rating.rating_ticket_email_template
msgid "Helpdesk Ticket: Rating Request"
msgstr "Ticket assistenza clienti: richiesta valutazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi hanno un errore di consegna."

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket_stage__rating_mail_template_id
msgid ""
"If set, an email will be sent to the customer  with a rating survey when the "
"ticket reaches this stage."
msgstr ""
"Se impostato, verrà inviata una mail al cliente  con un sondaggio di "
"valutazione quando il ticket raggiunge questa fase."

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr "Segue"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields.selection,name:helpdesk_mgmt_rating.selection__helpdesk_ticket__rating_status__no_rate
msgid "No rating"
msgstr "Nessuna valutazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__positive_rate_percentage
msgid "Positive Rates Percentage"
msgstr "Percentuale valutazioni positive"

#. module: helpdesk_mgmt_rating
#: model:ir.actions.act_window,name:helpdesk_mgmt_rating.helpdesk_ticket_rating_action
msgid "Rating"
msgstr "Valutazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_avg_text
msgid "Rating Avg Text"
msgstr "Testo valutazione media"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket_stage__rating_mail_template_id
msgid "Rating Email Template"
msgstr "Modello Email Valutazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Ultimo Feedback Valutazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr "Ultima immagine valutazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr "Ultimo valore valutazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Valutazione soddisfazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_text
msgid "Rating Text"
msgstr "Testo valutazione"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr "Conteggio valutazioni"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields.selection,name:helpdesk_mgmt_rating.selection__helpdesk_ticket__rating_status__stage_change
msgid "Rating when changing stage"
msgstr "Valutazione al cambiamento della fase"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_ids
#: model:mail.message.subtype,description:helpdesk_mgmt_rating.mt_ticket_rating
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.ticket_view_form_inherit
msgid "Ratings"
msgstr "Valutazioni"

#. module: helpdesk_mgmt_rating
#: model:mail.template,subject:helpdesk_mgmt_rating.rating_ticket_email_template
msgid "Satisfaction Survey of the ticket {{object.number}} - {{object.name}}"
msgstr ""
"Valutazione di gradimento della richiesta ${object.number} - ${object.name}"

#. module: helpdesk_mgmt_rating
#: model_terms:ir.actions.act_window,help:helpdesk_mgmt_rating.all_helpdesk_ticket_rating_action
msgid "There are no rated tickets at this moment."
msgstr "Non ci sono ticket valutati al momento."

#. module: helpdesk_mgmt_rating
#: model_terms:ir.actions.act_window,help:helpdesk_mgmt_rating.helpdesk_ticket_rating_action
msgid "There are no ratings for this ticket at the moment."
msgstr "Non ci sono valutazioni per questo ticket al momento."

#. module: helpdesk_mgmt_rating
#. odoo-python
#: code:addons/helpdesk_mgmt_rating/models/helpdesk_ticket.py:0
#: model:mail.message.subtype,name:helpdesk_mgmt_rating.mt_ticket_rating
msgid "Ticket Rating"
msgstr "Valutazione ticket"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#~ msgid "Rating Average"
#~ msgstr "Media valutazioni"

#~ msgid "Reason of the rating"
#~ msgstr "Motivo della valutazione"

#~ msgid ""
#~ "\n"
#~ "        % set access_token = object.rating_get_access_token()\n"
#~ "        <div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;"
#~ "font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
#~ "        <table style=\"width:600px;margin:5px auto;\">\n"
#~ "            <tbody>\n"
#~ "                <tr><td>\n"
#~ "                    <a href=\"/\"><img src=\"/web/binary/company_logo\" "
#~ "style=\"vertical-align:baseline;max-width:100px;\" /></a>\n"
#~ "                </td><td style=\"text-align:right;vertical-align:middle;"
#~ "\">\n"
#~ "                        Satisfaction Survey\n"
#~ "                </td></tr>\n"
#~ "            </tbody>\n"
#~ "        </table>\n"
#~ "        <table style=\"width:600px;margin:0px auto;background:white;"
#~ "border:1px solid #e1e1e1;\">\n"
#~ "          <tbody>\n"
#~ "              <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
#~ "                  <p>Hello,</p>\n"
#~ "                  <p>Please take a moment to rate our services related to "
#~ "the ticket \"<strong>${object.number} - ${object.name}</strong>\"\n"
#~ "                     assigned to <strong>${object."
#~ "rating_get_rated_partner_id().name}</strong>.</p>\n"
#~ "                  <p>We appreciate your feedback. It helps us to improve "
#~ "continuously.</p>\n"
#~ "              </td></tr>\n"
#~ "              <tr><td style=\"padding:10px 20px\">\n"
#~ "                  <table summary=\"o_mail_notification\" "
#~ "style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
#~ "                      <tr>\n"
#~ "                          <td style=\"text-align:center;\">\n"
#~ "                              <h2 style=\"font-weight:300;font-size:18px;"
#~ "\">\n"
#~ "                                  Tell us how you feel about our "
#~ "service:\n"
#~ "                              </h2>\n"
#~ "                              <div style=\"text-color: #888888\">(click "
#~ "on one of these smileys)</div>\n"
#~ "                          </td>\n"
#~ "                      </tr>\n"
#~ "                      <tr>\n"
#~ "                          <td style=\"padding:10px 10px;\">\n"
#~ "                              <table style=\"width:100%;text-align:center;"
#~ "\">\n"
#~ "                                  <tr>\n"
#~ "                                      <td>\n"
#~ "                                          <a href=\"/rate/"
#~ "${access_token}/5\">\n"
#~ "                                              <img alt=\"Satisfied\" "
#~ "src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
#~ "                                          </a>\n"
#~ "                                      </td>\n"
#~ "                                      <td>\n"
#~ "                                          <a href=\"/rate/"
#~ "${access_token}/3\">\n"
#~ "                                              <img alt=\"Not satisfied\" "
#~ "src=\"/rating/static/src/img/rating_3.png\" title=\"Not satisfied\"/>\n"
#~ "                                          </a>\n"
#~ "                                      </td>\n"
#~ "                                      <td>\n"
#~ "                                          <a href=\"/rate/"
#~ "${access_token}/1\">\n"
#~ "                                              <img alt=\"Highly "
#~ "Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Highly "
#~ "Dissatisfied\"/>\n"
#~ "                                          </a>\n"
#~ "                                      </td>\n"
#~ "                                  </tr>\n"
#~ "                              </table>\n"
#~ "                          </td>\n"
#~ "                      </tr>\n"
#~ "                  </table>\n"
#~ "              </td></tr>\n"
#~ "              <tr><td style=\"padding:15px 20px 10px 20px;\">${(object."
#~ "user_id.signature or '')| safe}</td></tr>\n"
#~ "          </tbody>\n"
#~ "      </table>\n"
#~ "      <table style=\"width:600px;margin:auto;text-align:center;font-"
#~ "size:12px;\">\n"
#~ "        <tbody>\n"
#~ "          <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
#~ "              % if object.rating_status == 'stage_change':\n"
#~ "                  <p>This customer survey has been sent because your "
#~ "ticket has been moved to the stage <b>${object.stage_id.name}</b></p>\n"
#~ "              % endif\n"
#~ "          </td></tr>\n"
#~ "        </tbody>\n"
#~ "      </table>\n"
#~ "    </div>\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "        % set access_token = object.rating_get_access_token()\n"
#~ "        <div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;"
#~ "font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
#~ "        <table style=\"width:600px;margin:5px auto;\">\n"
#~ "            <tbody>\n"
#~ "                <tr><td>\n"
#~ "                    <a href=\"/\"><img src=\"/web/binary/company_logo\" "
#~ "style=\"vertical-align:baseline;max-width:100px;\" /></a>\n"
#~ "                </td><td style=\"text-align:right;vertical-align:middle;"
#~ "\">\n"
#~ "                        Satisfaction Survey\n"
#~ "                </td></tr>\n"
#~ "            </tbody>\n"
#~ "        </table>\n"
#~ "        <table style=\"width:600px;margin:0px auto;background:white;"
#~ "border:1px solid #e1e1e1;\">\n"
#~ "          <tbody>\n"
#~ "              <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
#~ "                  <p>Ciao,</p>\n"
#~ "                  <p>Ti chiediamo pochi secondi del tuo tempo per "
#~ "valutare il servizio ricevuto in merito al ticket \"<strong>${object."
#~ "number} - ${object.name}</strong>\"\n"
#~ "                     assegnato a <strong>${object."
#~ "rating_get_rated_partner_id().name}</strong>.</p>\n"
#~ "                  <p>Ti ringraziamo per il tuo riscontro, che ci permette "
#~ "di continuare a migliorare.</p>\n"
#~ "              </td></tr>\n"
#~ "              <tr><td style=\"padding:10px 20px\">\n"
#~ "                  <table summary=\"o_mail_notification\" "
#~ "style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
#~ "                      <tr>\n"
#~ "                          <td style=\"text-align:center;\">\n"
#~ "                              <h2 style=\"font-weight:300;font-size:18px;"
#~ "\">\n"
#~ "                                  Dicci la tua opinione sul servizio:\n"
#~ "                              </h2>\n"
#~ "                              <div style=\"text-color: #888888\">(click "
#~ "on one of these smileys)</div>\n"
#~ "                          </td>\n"
#~ "                      </tr>\n"
#~ "                      <tr>\n"
#~ "                          <td style=\"padding:10px 10px;\">\n"
#~ "                              <table style=\"width:100%;text-align:center;"
#~ "\">\n"
#~ "                                  <tr>\n"
#~ "                                      <td>\n"
#~ "                                          <a href=\"/rate/"
#~ "${access_token}/5\">\n"
#~ "                                              <img alt=\"Soddisfacente\" "
#~ "src=\"/rating/static/src/img/rating_5.png\" title=\"Soddisfacente\"/>\n"
#~ "                                          </a>\n"
#~ "                                      </td>\n"
#~ "                                      <td>\n"
#~ "                                          <a href=\"/rate/"
#~ "${access_token}/3\">\n"
#~ "                                              <img "
#~ "alt=\"Insoddisfacente\" src=\"/rating/static/src/img/rating_3.png\" "
#~ "title=\"Insoddisfacente\"/>\n"
#~ "                                          </a>\n"
#~ "                                      </td>\n"
#~ "                                      <td>\n"
#~ "                                          <a href=\"/rate/"
#~ "${access_token}/1\">\n"
#~ "                                              <img alt=\"Molto "
#~ "insoddisfacente\" src=\"/rating/static/src/img/rating_1.png\" "
#~ "title=\"Molto insoddisfacente\"/>\n"
#~ "                                          </a>\n"
#~ "                                      </td>\n"
#~ "                                  </tr>\n"
#~ "                              </table>\n"
#~ "                          </td>\n"
#~ "                      </tr>\n"
#~ "                  </table>\n"
#~ "              </td></tr>\n"
#~ "              <tr><td style=\"padding:15px 20px 10px 20px;\">${(object."
#~ "user_id.signature or '')| safe}</td></tr>\n"
#~ "          </tbody>\n"
#~ "      </table>\n"
#~ "      <table style=\"width:600px;margin:auto;text-align:center;font-"
#~ "size:12px;\">\n"
#~ "        <tbody>\n"
#~ "          <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
#~ "              % if object.rating_status == 'stage_change':\n"
#~ "                  <p>Questo questionario ti è stato inviato perchè il "
#~ "ticket è stato spostato alla fase <b>${object.stage_id.name}</b></p>\n"
#~ "              % endif\n"
#~ "          </td></tr>\n"
#~ "        </tbody>\n"
#~ "      </table>\n"
#~ "    </div>\n"
#~ "    "

#~ msgid "Display Name"
#~ msgstr "Nome visualizzato"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Ultima modifica il"

#~ msgid "Satisfaction Survey of the ticket ${object.number} - ${object.name}"
#~ msgstr ""
#~ "Valutazione di gradimento del ticket ${object.number} - ${object.name}"
