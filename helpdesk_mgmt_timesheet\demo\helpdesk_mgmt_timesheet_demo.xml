<?xml version="1.0" encoding="utf-8" ?>
<odoo noupdate="1">
    <record id="project_1" model="project.project">
        <field name="name">Helpdesk general project</field>
    </record>
    <record id="project_task_1" model="project.task">
        <field name="name">Helpdesk general task</field>
        <field name="project_id" ref="project_1" />
    </record>
    <record id="helpdesk_mgmt.helpdesk_team_2" model="helpdesk.ticket.team">
        <field name="allow_timesheet" eval="True" />
        <field name="default_project_id" ref="project_1" />
    </record>
    <record id="helpdesk_mgmt.helpdesk_ticket_1" model="helpdesk.ticket">
        <field name="team_id" ref="helpdesk_mgmt.helpdesk_team_2" />
        <field name="project_id" ref="project_1" />
        <field name="task_id" ref="project_task_1" />
        <field name="planned_hours" eval="5" />
    </record>
    <record id="helpdesk_ticket_1_timesheet_1" model="account.analytic.line">
        <field name="ticket_id" ref="helpdesk_mgmt.helpdesk_ticket_1" />
        <field name="name">Initial analysis</field>
        <field name="user_id" ref='base.user_admin' />
        <field name="project_id" ref="project_1" />
        <field name="task_id" ref="project_task_1" />
        <field name="unit_amount" eval="2.5" />
    </record>
    <record id="helpdesk_ticket_1_timesheet_2" model="account.analytic.line">
        <field name="ticket_id" ref="helpdesk_mgmt.helpdesk_ticket_1" />
        <field name="name">Resolution</field>
        <field name="user_id" ref='base.user_admin' />
        <field name="project_id" ref="project_1" />
        <field name="task_id" ref="project_task_1" />
        <field name="unit_amount" eval="2" />
    </record>
</odoo>
