<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="report_timesheet" inherit_id="hr_timesheet.report_timesheet">
        <xpath expr="//t[@t-set='show_project']" position="after">
            <t
                t-set="show_ticket"
                t-value="bool(docs.mapped('ticket_id'))"
                groups="helpdesk_mgmt.group_helpdesk_user"
            />
        </xpath>
    </template>
    <template id="timesheet_table" inherit_id="hr_timesheet.timesheet_table">
        <xpath expr="//table[hasclass('table-sm')]/thead/tr/th[3]" position="after">
            <th
                t-if="show_ticket"
                groups="helpdesk_mgmt.group_helpdesk_user"
            >Ticket</th>
        </xpath>
        <xpath expr="//table[hasclass('table-sm')]/tbody/tr[1]/td[3]" position="after">
            <td t-if="show_ticket" groups="helpdesk_mgmt.group_helpdesk_user">
                <span t-field="line.ticket_id.number" /> - <span
                t-field="line.ticket_id.name"
            />
            </td>
        </xpath>
        <xpath expr="//table[hasclass('table-sm')]/tbody/tr[2]/td" position="before">
            <t
                t-if="show_ticket"
                t-set="nbCols"
                t-value="nbCols + 1"
                groups="helpdesk_mgmt.group_helpdesk_user"
            />
        </xpath>
    </template>
</odoo>
