<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_helpdesk_ticket_stage_search" model="ir.ui.view">
        <field name="name">helpdesk.ticket.stage.search</field>
        <field name="model">helpdesk.ticket.stage</field>
        <field name="arch" type="xml">
            <search string="Ticket Stage Search">
                <filter
                    string="Archived"
                    name="inactive"
                    domain="[('active','=',False)]"
                />
                <separator />
                <field name="name" filter_domain="[('name', 'ilike', self)]" />
                <field name="company_id" groups="base.group_multi_company" />
                <field name="team_ids" />
                <group>
                    <filter
                        string="Company"
                        name="company"
                        context="{'group_by': 'company_id'}"
                        groups="base.group_multi_company"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="view_helpdesk_ticket_stage_form" model="ir.ui.view">
        <field name="name">helpdesk.ticket.stage.form</field>
        <field name="model">helpdesk.ticket.stage</field>
        <field name="arch" type="xml">
            <form string="Stages">
                <header />
                <sheet>
                    <widget
                        name="web_ribbon"
                        title="Archived"
                        bg_color="bg-danger"
                        invisible="active"
                    />
                    <div class="oe_button_box" name="button_box" />
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only" />
                        <h1>
                            <field name="name" required="1" />
                        </h1>
                    </div>
                    <group name="main">
                        <group name="main_left">
                            <field name="mail_template_id" />
                            <field name="team_ids" widget="many2many_tags" />
                            <field
                                name="company_id"
                                groups="base.group_multi_company"
                            />
                            <field name="company_id" invisible="1" />
                        </group>
                        <group name="main_right">
                            <field name="active" invisible="1" />
                            <field name="closed" />
                            <field name="close_from_portal" invisible="not closed" />
                            <field name="fold" />
                            <field name="unattended" />
                        </group>
                    </group>
                    <field name="description" widget="html" />
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_helpdesk_ticket_stage_tree" model="ir.ui.view">
        <field name="name">helpdesk.ticket.stage.list</field>
        <field name="model">helpdesk.ticket.stage</field>
        <field name="arch" type="xml">
            <list>
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="mail_template_id" />
                <field name="team_ids" widget="many2many_tags" />
                <field
                    name="company_id"
                    optional="hide"
                    groups="base.group_multi_company"
                />
            </list>
        </field>
    </record>
</odoo>
