===============
Helpdesk Motive
===============

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:79e6872e14e06e5d76051f709fe0462b3d9d495b84141992e5f669ef566f8325
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fhelpdesk-lightgray.png?logo=github
    :target: https://github.com/OCA/helpdesk/tree/18.0/helpdesk_motive
    :alt: OCA/helpdesk
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/helpdesk-18-0/helpdesk-18-0-helpdesk_motive
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/helpdesk&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module adds a motive field on the helpdesk ticket. The list is
filtered based on the helpdesk team.

**Table of contents**

.. contents::
   :local:

Configuration
=============

- Go to Helpdesk > Configuration > Motives
- Create your list of different motives with their name and teams

Usage
=====

- Go to Helpdesk
- Create a ticket and set its motive. The list of motives is limited by
  the team.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/helpdesk/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/helpdesk/issues/new?body=module:%20helpdesk_motive%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Binhex
* Konos
* Open Source Integrators

Contributors
------------

- `Open Source Integrators <https://www.opensourceintegrators.com>`__:

  - Maxime Chambreuil <<EMAIL>>

- `Konos <https://www.konos.cl>`__:

  - Nelson Sanchez <<EMAIL>>

- `Solvos <https://www.solvos.es>`__:

  - David Alonso <<EMAIL>>

- \`Binhex <https://binhex.cloud>\_\`:

  - Adasat Torres <<EMAIL>>

- `Heliconia Solutions Pvt. Ltd. <https://www.heliconia.io>`__

  - Bhavesh Heliconia

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-nelsonramirezs| image:: https://github.com/nelsonramirezs.png?size=40px
    :target: https://github.com/nelsonramirezs
    :alt: nelsonramirezs
.. |maintainer-max3903| image:: https://github.com/max3903.png?size=40px
    :target: https://github.com/max3903
    :alt: max3903

Current `maintainers <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-nelsonramirezs| |maintainer-max3903| 

This module is part of the `OCA/helpdesk <https://github.com/OCA/helpdesk/tree/18.0/helpdesk_motive>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
