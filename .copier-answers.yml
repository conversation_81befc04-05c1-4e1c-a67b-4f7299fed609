# Do NOT update manually; changes here will be overwritten by Copier
_commit: v1.29
_src_path: git+https://github.com/OCA/oca-addons-repo-template
additional_ruff_rules: []
ci: GitHub
convert_readme_fragments_to_markdown: true
enable_checklog_odoo: true
generate_requirements_txt: true
github_check_license: true
github_ci_extra_env: {}
github_enable_codecov: true
github_enable_makepot: true
github_enable_stale_action: true
github_enforce_dev_status_compatibility: true
include_wkhtmltopdf: false
odoo_test_flavor: Both
odoo_version: 18.0
org_name: Odoo Community Association (OCA)
org_slug: OCA
rebel_module_groups: []
repo_description: helpdesk
repo_name: helpdesk
repo_slug: helpdesk
repo_website: https://github.com/OCA/helpdesk
use_pyproject_toml: true
use_ruff: true

