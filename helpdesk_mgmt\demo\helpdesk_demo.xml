<?xml version="1.0" ?>
<odoo noupdate="1">
    <!-- User Demo -->
    <record id="base.user_demo" model="res.users">
        <field name="groups_id" eval="[(4,ref('group_helpdesk_user'))]" />
    </record>
    <!-- Categories -->
    <record id="helpdesk_category_1" model="helpdesk.ticket.category">
        <field eval="&quot;Software&quot;" name="name" />
        <field name="company_id" ref="base.main_company" />
        <field eval="&quot;1&quot;" name="active" />
    </record>
    <record id="helpdesk_category_2" model="helpdesk.ticket.category">
        <field eval="&quot;Wifi&quot;" name="name" />
        <field name="company_id" ref="base.main_company" />
        <field eval="&quot;1&quot;" name="active" />
    </record>
    <record id="helpdesk_category_3" model="helpdesk.ticket.category">
        <field eval="&quot;Odoo&quot;" name="name" />
        <field name="company_id" ref="base.main_company" />
        <field eval="&quot;1&quot;" name="active" />
    </record>
    <record id="helpdesk_category_4" model="helpdesk.ticket.category">
        <field eval="&quot;Hardware&quot;" name="name" />
        <field name="company_id" ref="base.main_company" />
        <field eval="&quot;1&quot;" name="active" />
    </record>
    <record id="helpdesk_category_5" model="helpdesk.ticket.category">
        <field eval="&quot;Laptops&quot;" name="name" />
        <field name="company_id" ref="base.main_company" />
        <field eval="&quot;1&quot;" name="active" />
        <field name="parent_id" ref="helpdesk_category_4" />
    </record>
    <!-- Mail aliases -->
    <record id="mail_alias_1" model="mail.alias">
        <field eval="&quot;General Alias for tickets&quot;" name="alias_name" />
        <field ref="model_helpdesk_ticket" name="alias_model_id" />
    </record>
    <record id="mail_alias_2" model="mail.alias">
        <field eval="&quot;General Alias 2 for tickets&quot;" name="alias_name" />
        <field ref="model_helpdesk_ticket" name="alias_model_id" />
    </record>
    <record id="mail_alias_3" model="mail.alias">
        <field eval="&quot;General Alias 3 for tickets&quot;" name="alias_name" />
        <field ref="model_helpdesk_ticket" name="alias_model_id" />
    </record>

    <!-- Teams -->
    <record id="helpdesk_team_1" model="helpdesk.ticket.team">
        <field eval="&quot;Localization team&quot;" name="name" />
        <field
            name="user_ids"
            eval="[(6,0,[ref('base.user_root'), ref('base.user_demo')])]"
        />
        <field eval="&quot;1&quot;" name="active" />
        <field
            name="category_ids"
            eval="[(6,0,[ref('helpdesk_category_1'), ref('helpdesk_category_2')])]"
        />
        <field name="company_id" ref="base.main_company" />
        <field name="alias_id" ref="mail_alias_1" />
    </record>
    <record id="helpdesk_team_2" model="helpdesk.ticket.team">
        <field name="name">Helpdesk</field>
        <field name="user_ids" eval="[(6,0,[ref('base.user_demo')])]" />
        <field eval="&quot;1&quot;" name="active" />
        <field name="category_ids" eval="[(6,0,[ref('helpdesk_category_1')])]" />
        <field name="company_id" ref="base.main_company" />
        <field name="alias_id" ref="mail_alias_2" />
    </record>
    <record id="helpdesk_team_3" model="helpdesk.ticket.team">
        <field eval="&quot;Consultants&quot;" name="name" />
        <field name="user_ids" eval="[(6,0,[ref('base.user_root')])]" />
        <field eval="&quot;1&quot;" name="active" />
        <field name="category_ids" eval="[(6,0,[ref('helpdesk_category_2')])]" />
        <field name="company_id" ref="base.main_company" />
        <field name="alias_id" ref="mail_alias_3" />
    </record>
    <!-- Tags -->
    <record id="helpdesk_tag_1" model="helpdesk.ticket.tag">
        <field eval="&quot;Hard&quot;" name="name" />
        <field eval="&quot;1&quot;" name="color" />
        <field eval="&quot;1&quot;" name="active" />
        <field name="company_id" ref="base.main_company" />
    </record>
    <record id="helpdesk_tag_2" model="helpdesk.ticket.tag">
        <field eval="&quot;Technical&quot;" name="name" />
        <field eval="&quot;2&quot;" name="color" />
        <field eval="&quot;1&quot;" name="active" />
        <field name="company_id" ref="base.main_company" />
    </record>
    <record id="helpdesk_tag_3" model="helpdesk.ticket.tag">
        <field eval="&quot;Functional&quot;" name="name" />
        <field eval="&quot;3&quot;" name="color" />
        <field eval="&quot;1&quot;" name="active" />
        <field name="company_id" ref="base.main_company" />
    </record>
    <!-- Tickets -->
    <record id="helpdesk_ticket_1" model="helpdesk.ticket">
        <field eval="&quot;Problem with the delivery of goods&quot;" name="name" />
        <field
            eval="&quot;Problem with the delivery of goods&quot;"
            name="description"
        />
        <field eval="&quot;1&quot;" name="priority" />
        <field name="user_id" ref="base.user_root" />
        <field name="stage_id" ref="helpdesk_ticket_stage_new" />
        <field name="channel_id" ref="helpdesk_ticket_channel_web" />
        <field name="partner_id" ref="base.res_partner_3" />
        <field name="team_id" ref="helpdesk_team_2" />
    </record>
    <record id="helpdesk_ticket_2" model="helpdesk.ticket">
        <field eval="&quot;Damaged Products&quot;" name="name" />
        <field eval="&quot;Damaged Products&quot;" name="description" />
        <field eval="&quot;Alison Hardwell&quot;" name="partner_name" />
        <field eval="&quot;0&quot;" name="priority" />
        <field name="user_id" ref="base.user_demo" />
        <field name="stage_id" ref="helpdesk_ticket_stage_new" />
        <field name="channel_id" ref="helpdesk_ticket_channel_web" />
        <field name="partner_id" ref="base.res_partner_2" />
        <field name="team_id" ref="helpdesk_team_1" />
    </record>
    <record id="helpdesk_ticket_3" model="helpdesk.ticket">
        <field eval="&quot;Document related problems&quot;" name="name" />
        <field eval="&quot;Document related problems&quot;" name="description" />
        <field eval="&quot;Jaime Bajozano&quot;" name="partner_name" />
        <field eval="&quot;<EMAIL>&quot;" name="partner_email" />
        <field eval="&quot;1&quot;" name="priority" />
        <field eval="[(6,0,[ref('helpdesk_tag_1')])]" name="tag_ids" />
        <field name="user_id" ref="base.user_root" />
        <field name="stage_id" ref="helpdesk_ticket_stage_cancelled" />
        <field name="channel_id" ref="helpdesk_ticket_channel_other" />
        <field name="partner_id" ref="base.res_partner_18" />
        <field name="team_id" ref="helpdesk_team_1" />
    </record>
    <record id="helpdesk_ticket_4" model="helpdesk.ticket">
        <field eval="&quot;Product quality not maintained&quot;" name="name" />
        <field eval="&quot;Product quality not maintained&quot;" name="description" />
        <field eval="&quot;Jamie Mads&quot;" name="partner_name" />
        <field eval="&quot;2&quot;" name="priority" />
        <field name="user_id" ref="base.user_demo" />
        <field name="stage_id" ref="helpdesk_ticket_stage_in_progress" />
        <field name="partner_id" ref="base.res_partner_10" />
        <field name="team_id" ref="helpdesk_team_3" />
        <field name="channel_id" ref="helpdesk_ticket_channel_phone" />
    </record>
    <record id="helpdesk_ticket_5" model="helpdesk.ticket">
        <field eval="&quot;Some products missing&quot;" name="name" />
        <field eval="&quot;Some products missing&quot;" name="description" />
        <field eval="&quot;3&quot;" name="priority" />
        <field name="user_id" ref="base.user_root" />
        <field name="partner_id" ref="base.res_partner_12" />
        <field name="team_id" ref="helpdesk_team_3" />
        <field name="stage_id" ref="helpdesk_ticket_stage_in_progress" />
    </record>
    <record id="helpdesk_ticket_6" model="helpdesk.ticket">
        <field
            eval="&quot;Problem with the delivery of assignments&quot;"
            name="name"
        />
        <field
            eval="&quot;Problem with the delivery of assignments&quot;"
            name="description"
        />
        <field eval="&quot;1&quot;" name="priority" />
        <field eval="[(6,0,[ref('helpdesk_tag_2')])]" name="tag_ids" />
        <field name="user_id" ref="base.user_root" />
        <field name="stage_id" ref="helpdesk_ticket_stage_awaiting" />
        <field name="partner_id" ref="base.res_partner_12" />
        <field name="team_id" ref="helpdesk_team_3" />
    </record>
    <record id="helpdesk_ticket_7" model="helpdesk.ticket">
        <field eval="&quot;Documents unclear&quot;" name="name" />
        <field eval="&quot;Documents unclear&quot;" name="description" />
        <field eval="&quot;1&quot;" name="priority" />
        <field eval="[(6,0,[ref('helpdesk_tag_3')])]" name="tag_ids" />
        <field name="user_id" ref="base.user_root" />
        <field name="stage_id" ref="helpdesk_ticket_stage_done" />
        <field name="partner_id" ref="base.res_partner_2" />
        <field name="team_id" ref="helpdesk_team_1" />
    </record>
    <record id="helpdesk_ticket_8" model="helpdesk.ticket">
        <field eval="&quot;Product quality not maintained&quot;" name="name" />
        <field eval="&quot;Product quality not maintained&quot;" name="description" />
        <field eval="&quot;Jamie Mads&quot;" name="partner_name" />
        <field eval="&quot;2&quot;" name="priority" />
        <field name="user_id" ref="base.user_demo" />
        <field name="stage_id" ref="helpdesk_ticket_stage_in_progress" />
        <field name="partner_id" ref="base.res_partner_10" />
        <field name="team_id" ref="helpdesk_team_3" />
        <field name="category_id" ref="helpdesk_category_5" />
    </record>
</odoo>
