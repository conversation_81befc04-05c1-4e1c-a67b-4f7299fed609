# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt_project
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-19 08:40+0000\n"
"PO-Revision-Date: 2023-08-07 13:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: helpdesk_mgmt_project
#: model:ir.model.fields,help:helpdesk_mgmt_project.field_project_project__label_tickets
#: model:ir.model.fields,help:helpdesk_mgmt_project.field_project_task__label_tickets
msgid "Gives label to tickets on project's kanban view."
msgstr "Da etiqueta a las entradas en la vista kanban del proyecto."

#. module: helpdesk_mgmt_project
#: model:ir.model,name:helpdesk_mgmt_project.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket Helpdesk"

#. module: helpdesk_mgmt_project
#: model:ir.model,name:helpdesk_mgmt_project.model_helpdesk_ticket_team
msgid "Helpdesk Ticket Team"
msgstr "Equipo de asistencia para entradas"

#. module: helpdesk_mgmt_project
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_project_project__todo_ticket_count
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_project_task__todo_ticket_count
msgid "Number of tickets"
msgstr "Número de tickets"

#. module: helpdesk_mgmt_project
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_project.view_project_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_project.view_task_search
msgid "Open Tickets"
msgstr "Tickets abiertos"

#. module: helpdesk_mgmt_project
#: model:ir.model,name:helpdesk_mgmt_project.model_project_project
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_helpdesk_ticket__project_id
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_helpdesk_ticket_team__default_project_id
msgid "Project"
msgstr "Proyecto"

#. module: helpdesk_mgmt_project
#: model:ir.model,name:helpdesk_mgmt_project.model_project_task
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_helpdesk_ticket__task_id
msgid "Task"
msgstr "Tarea"

#. module: helpdesk_mgmt_project
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_project_project__ticket_count
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_project_task__ticket_count
msgid "Ticket Count"
msgstr "Contador Tickets"

#. module: helpdesk_mgmt_project
#. odoo-python
#: code:addons/helpdesk_mgmt_project/models/project.py:0
#: code:addons/helpdesk_mgmt_project/models/project_task.py:0
#: model:ir.actions.act_window,name:helpdesk_mgmt_project.ticket_action_from_project
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_project_project__ticket_ids
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_project_task__ticket_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_project.edit_project
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_project.view_task_form2
#, python-format
msgid "Tickets"
msgstr "Tiquets"

#. module: helpdesk_mgmt_project
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_project_project__label_tickets
#: model:ir.model.fields,field_description:helpdesk_mgmt_project.field_project_task__label_tickets
msgid "Use Tickets as"
msgstr "Usar tickets como"
