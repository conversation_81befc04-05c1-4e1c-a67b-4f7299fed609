# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt_rating
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2024-07-27 05:58+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: helpdesk_mgmt_rating
#: model:mail.template,body_html:helpdesk_mgmt_rating.rating_ticket_email_template
#, fuzzy
msgid ""
"<div>\n"
"                <t t-set=\"access_token\" t-value=\"object."
"_rating_get_access_token()\"/>\n"
"                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                                <p>Hello,</p>\n"
"                                <p>Please take a moment to rate our services "
"related to the ticket \"<strong><t t-out=\"object.number\"/> - <t t-"
"out=\"object.name\"/></strong>\"assigned to <strong>\n"
"                                    <t t-out=\"object._rating_get_operator()."
"name\"/>\n"
"                                </strong>.</p>\n"
"                                <p>We appreciate your feedback. It helps us "
"to improve continuously.</p>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <td style=\"padding:10px 20px\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" "
"style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                                    <tr>\n"
"                                        <td style=\"text-align:center;\">\n"
"                                            <h2 style=\"font-weight:300;font-"
"size:18px;\">Tell us how you feel about our service:</h2>\n"
"                                            <div style=\"text-color: "
"#888888\">(click on one of these smileys)</div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td style=\"padding:10px 10px;\">\n"
"                                            <table style=\"width:100%;text-"
"align:center;margin-top:2rem;\">\n"
"                                                <tr>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/5\">\n"
"                                                            <img "
"alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" "
"title=\"Satisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/3\">\n"
"                                                            <img alt=\"Not "
"satisfied\" src=\"/rating/static/src/img/rating_3.png\" title=\"Not "
"satisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/1\">\n"
"                                                            <img "
"alt=\"Highly Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" "
"title=\"Highly Dissatisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:auto;text-align:center;"
"font-size:12px;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td style=\"padding-top:10px;color:#afafaf;\">\n"
"                                <t t-if=\"object.rating_status == "
"'stage_change'\">\n"
"                                    <p>This customer survey has been sent "
"because your ticket has been moved to the stage <b t-out=\"object.stage_id."
"name\"/></p>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""
"<div>\n"
"                <t t-set=\"access_token\" t-value=\"object."
"_rating_get_access_token()\"></t>\n"
"                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                                <p>Olá,</p>\n"
"                                <p>Por favor, reserve um momento para "
"avaliar os nossos serviços relativos ao ticket \"<strong><t t-out=\"object."
"number\"></t> - <t t-out=\"object.name\"></t></strong>\"atribuído a "
"<strong><t t-out=\"object._rating_get_operator().name\"></t></strong>.</p>\n"
"                                <p>Agradecemos o seu feedback. Ajuda-nos a "
"melhorar continuamente.</p>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <td style=\"padding:10px 20px\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" "
"style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                                    <tr>\n"
"                                        <td style=\"text-align:center;\">\n"
"                                            <h2 style=\"font-weight:300;font-"
"size:18px;\">Diga-nos o que sente quanto ao nosso serviço:</h2>\n"
"                                            <div style=\"text-color: "
"#888888\">(clique num destes smileys)</div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td style=\"padding:10px 10px;\">\n"
"                                            <table style=\"width:100%;text-"
"align:center;margin-top:2rem;\">\n"
"                                                <tr>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/5\">\n"
"                                                            <img "
"alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" "
"title=\"Satisfeito\">\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/3\">\n"
"                                                            <img alt=\"Not "
"satisfied\" src=\"/rating/static/src/img/rating_3.png\" "
"title=\"Insatisfeito\">\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/1\">\n"
"                                                            <img "
"alt=\"Highly Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" "
"title=\"Extremamente Insatisfeito\">\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:auto;text-align:center;"
"font-size:12px;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td style=\"padding-top:10px;color:#afafaf;\">\n"
"                                <t t-if=\"object.rating_status == "
"'stage_change'\">\n"
"                                    <p>Este inquérito de satisfação foi "
"enviado porque o seu ticket foi alterado para o estado <b t-out=\"object."
"stage_id.name\"></b></p>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "

#. module: helpdesk_mgmt_rating
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.view_helpdesk_ticket_kanban_inherit
msgid ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"
msgstr ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentagem de satisfação\"/>"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_avg
msgid "Average Rating"
msgstr "Avaliação Média"

#. module: helpdesk_mgmt_rating
#: model:ir.actions.act_window,name:helpdesk_mgmt_rating.all_helpdesk_ticket_rating_action
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_status
#: model:ir.ui.menu,name:helpdesk_mgmt_rating.helpdesk_ticket_rating_menu
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.view_helpdesk_ticket_kanban_inherit
msgid "Customer Rating"
msgstr "Avaliação do Cliente"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__has_message
msgid "Has Message"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model,name:helpdesk_mgmt_rating.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket de Helpdesk"

#. module: helpdesk_mgmt_rating
#: model:ir.model,name:helpdesk_mgmt_rating.model_helpdesk_ticket_stage
msgid "Helpdesk Ticket Stage"
msgstr "Estado do Ticket de Helpdesk"

#. module: helpdesk_mgmt_rating
#: model:mail.template,name:helpdesk_mgmt_rating.rating_ticket_email_template
msgid "Helpdesk Ticket: Rating Request"
msgstr "Ticket de Helpdesk: Solicitação de Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket_stage__rating_mail_template_id
msgid ""
"If set, an email will be sent to the customer  with a rating survey when the "
"ticket reaches this stage."
msgstr ""
"Se definido, será enviado um e-mail ao cliente com um inquérito de "
"satisfação quando o ticket chegar a este estado."

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields.selection,name:helpdesk_mgmt_rating.selection__helpdesk_ticket__rating_status__no_rate
msgid "No rating"
msgstr "Sem avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__positive_rate_percentage
msgid "Positive Rates Percentage"
msgstr "Percentagem de Avaliações Positivas"

#. module: helpdesk_mgmt_rating
#: model:ir.actions.act_window,name:helpdesk_mgmt_rating.helpdesk_ticket_rating_action
msgid "Rating"
msgstr "Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_avg_text
msgid "Rating Avg Text"
msgstr "Média de Texto de Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket_stage__rating_mail_template_id
msgid "Rating Email Template"
msgstr "Modelo de E-mail de Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Último Feedback de Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr "Última Imagem de Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr "Último Valor de Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Satisfação da Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_text
msgid "Rating Text"
msgstr "Texto da Avaliação"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr "Contagem de Avaliações"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields.selection,name:helpdesk_mgmt_rating.selection__helpdesk_ticket__rating_status__stage_change
msgid "Rating when changing stage"
msgstr "Avaliação ao mudar de estado"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_ids
#: model:mail.message.subtype,description:helpdesk_mgmt_rating.mt_ticket_rating
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.ticket_view_form_inherit
msgid "Ratings"
msgstr "Avaliações"

#. module: helpdesk_mgmt_rating
#: model:mail.template,subject:helpdesk_mgmt_rating.rating_ticket_email_template
msgid "Satisfaction Survey of the ticket {{object.number}} - {{object.name}}"
msgstr "Inquérito de avaliação do ticket {{object.number}} - {{object.name}}"

#. module: helpdesk_mgmt_rating
#: model_terms:ir.actions.act_window,help:helpdesk_mgmt_rating.all_helpdesk_ticket_rating_action
msgid "There are no rated tickets at this moment."
msgstr "Não há tickets avaliados de momento."

#. module: helpdesk_mgmt_rating
#: model_terms:ir.actions.act_window,help:helpdesk_mgmt_rating.helpdesk_ticket_rating_action
msgid "There are no ratings for this ticket at the moment."
msgstr "Não há avaliações para este ticket de momento."

#. module: helpdesk_mgmt_rating
#. odoo-python
#: code:addons/helpdesk_mgmt_rating/models/helpdesk_ticket.py:0
#: model:mail.message.subtype,name:helpdesk_mgmt_rating.mt_ticket_rating
msgid "Ticket Rating"
msgstr "Avaliação de Ticket"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr ""
