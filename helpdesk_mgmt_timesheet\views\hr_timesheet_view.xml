<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="hr_timesheet_line_search" model="ir.ui.view">
        <field name="model">account.analytic.line</field>
        <field name="inherit_id" ref="hr_timesheet.hr_timesheet_line_search" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="ticket_id" groups="helpdesk_mgmt.group_helpdesk_user" />
                <field
                    name="ticket_partner_id"
                    groups="helpdesk_mgmt.group_helpdesk_user"
                />
            </xpath>
            <xpath expr="//filter[@name='month']" position="before">
                <filter
                    name="ticket"
                    string="With ticket"
                    domain="[('ticket_id', '!=', False)]"
                    groups="helpdesk_mgmt.group_helpdesk_user"
                />
                <separator />
            </xpath>
            <xpath expr="//filter[@name='groupby_employee']" position="before">
                <filter
                    string="Ticket partner"
                    name="groupby_ticket_partner"
                    domain="[]"
                    context="{'group_by':'ticket_partner_id'}"
                    groups="helpdesk_mgmt.group_helpdesk_user"
                />
                <filter
                    string="Ticket"
                    name="groupby_ticket"
                    domain="[]"
                    context="{'group_by':'ticket_id'}"
                    groups="helpdesk_mgmt.group_helpdesk_user"
                />
            </xpath>
        </field>
    </record>
    <record id="hr_timesheet_line_tree" model="ir.ui.view">
        <field name="model">account.analytic.line</field>
        <field name="inherit_id" ref="hr_timesheet.hr_timesheet_line_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field
                    name="ticket_partner_id"
                    groups="helpdesk_mgmt.group_helpdesk_user"
                />
                <field name="ticket_id" groups="helpdesk_mgmt.group_helpdesk_user" />
            </xpath>
        </field>
    </record>
    <record id="hr_timesheet_line_form" model="ir.ui.view">
        <field name="model">account.analytic.line</field>
        <field name="inherit_id" ref="hr_timesheet.hr_timesheet_line_form" />
        <field name="arch" type="xml">
            <field name="task_id" position="after">
                <field
                    name="ticket_id"
                    required="context.get('ticket_required', False)"
                    groups="helpdesk_mgmt.group_helpdesk_user"
                />
                <field
                    name="ticket_partner_id"
                    invisible="not ticket_id"
                    groups="helpdesk_mgmt.group_helpdesk_user"
                />
            </field>
        </field>
    </record>
    <record id="helpdesk_timesheet_action" model="ir.actions.act_window">
        <field name="name">Timesheets</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.analytic.line</field>
        <field name="view_mode">list,kanban,form,pivot</field>
        <field name="domain">[('ticket_id', '!=', False)]</field>
        <field name="context">{
            'ticket_required': True,
            'search_default_groupby_ticket_partner': 1,
            'search_default_groupby_ticket': 1,
            }</field>
    </record>
    <record id="act_hr_timesheet_line_view_form" model="ir.actions.act_window.view">
        <field name="view_mode">form</field>
        <field name="sequence" eval="5" />
        <field name="view_id" ref="hr_timesheet.hr_timesheet_line_form" />
        <field name="act_window_id" ref="helpdesk_timesheet_action" />
    </record>
    <record id="act_hr_timesheet_line_view_tree" model="ir.actions.act_window.view">
        <field name="view_mode">list</field>
        <field name="sequence" eval="4" />
        <field name="view_id" ref="hr_timesheet.timesheet_view_tree_user" />
        <field name="act_window_id" ref="helpdesk_timesheet_action" />
    </record>
    <menuitem
        id="helpdesk_timesheet_menu"
        name="Timesheets"
        parent="helpdesk_mgmt.helpdesk_ticket_main_menu"
        action="helpdesk_timesheet_action"
        groups="hr_timesheet.group_hr_timesheet_user"
        sequence="15"
    />
</odoo>
