<?xml version='1.0' encoding='utf-8' ?>
<!-- Copyright 2024 Tecnativa - Pilar Vargas
     License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="ticket_view_form" model="ir.ui.view">
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk_mgmt.ticket_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button
                    name="action_view_sale_orders"
                    type="object"
                    class="oe_stat_button"
                    icon="fa-dollar"
                    groups="sales_team.group_sale_salesman"
                >
                    <field string="Sale Orders" name="so_count" widget="statinfo" />
                </button>
            </xpath>
        </field>
    </record>
    <record id="action_helpdesk_ticket" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('sale_order_ids', 'in', active_id)]</field>
        <field name="context">{'default_sale_order_ids': active_ids}</field>
    </record>
</odoo>
