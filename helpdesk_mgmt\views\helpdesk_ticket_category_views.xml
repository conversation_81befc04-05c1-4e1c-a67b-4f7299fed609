<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_helpdesk_ticket_category_search" model="ir.ui.view">
        <field name="name">helpdesk.ticket.category.search</field>
        <field name="model">helpdesk.ticket.category</field>
        <field name="arch" type="xml">
            <search string="Helpdesk Category Search">
                <filter
                    string="Archived"
                    name="inactive"
                    domain="[('active','=',False)]"
                />
                <separator />
                <field name="name" filter_domain="[('name', 'ilike', self)]" />
                <field name="parent_id" />
                <field name="company_id" groups="base.group_multi_company" />
                <group>
                    <filter
                        string="Company"
                        name="company"
                        context="{'group_by': 'company_id'}"
                        groups="base.group_multi_company"
                    />
                    <filter
                        string="Parent Category"
                        name="parent_id"
                        context="{'group_by': 'parent_id'}"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="view_helpdesk_category_form" model="ir.ui.view">
        <field name="name">view.helpdesk_category.form</field>
        <field name="model">helpdesk.ticket.category</field>
        <field name="arch" type="xml">
            <form string="Stages">
                <header />
                <sheet>
                    <widget
                        name="web_ribbon"
                        title="Archived"
                        bg_color="bg-danger"
                        invisible="active"
                    />

                    <div class="oe_title">
                        <label for="name" class="oe_edit_only" />
                        <h1>
                            <field name="name" required="1" />
                        </h1>
                    </div>
                    <group name="main">
                        <field name="parent_id" />
                        <field name="child_id" widget="many2many_tags" readonly="1" />
                        <field name="company_id" groups="base.group_multi_company" />
                        <field name="active" invisible="1" />
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_helpdesk_category_tree" model="ir.ui.view">
        <field name="name">view.helpdesk_category.list</field>
        <field name="model">helpdesk.ticket.category</field>
        <field name="arch" type="xml">
            <list editable="bottom">
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="parent_id" />
                <field
                    name="company_id"
                    optional="hide"
                    groups="base.group_multi_company"
                />
            </list>
        </field>
    </record>
</odoo>
