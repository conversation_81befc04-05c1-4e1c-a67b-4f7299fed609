<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="ticket_action_from_project" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="helpdesk_mgmt.helpdesk_ticket_view_search" />
    </record>
    <!-- TREE TICKET HELP DESK -->
    <record id="ticket_view_tree" model="ir.ui.view">
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk_mgmt.ticket_view_tree" />
        <field name="arch" type="xml">
            <field name="user_id" position="after">
                <field name="project_id" groups="project.group_project_user" />
            </field>
        </field>
    </record>
    <!-- SEARCH TICKET HELP DESK -->
    <record id="helpdesk_ticket_view_search" model="ir.ui.view">
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk_mgmt.helpdesk_ticket_view_search" />
        <field name="arch" type="xml">
            <field name="user_id" position="after">
                <field name="project_id" groups="project.group_project_user" />
                <field name="task_id" groups="project.group_project_user" />
            </field>
        </field>
    </record>
    <!-- View Form Ticket -->
    <record id="ticket_view_form" model="ir.ui.view">
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk_mgmt.ticket_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_email']" position="after">
                <field name="project_id" invisible="1" />
                <field name="project_id" groups="project.group_project_user" />
                <field name="task_id" invisible="1" />
                <field
                    name="task_id"
                    domain="[('project_id', '=', project_id)]"
                    invisible="not project_id"
                    context="{'default_project_id': project_id}"
                    groups="project.group_project_user"
                />
            </xpath>
        </field>
    </record>
</odoo>
