# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt_merge
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-01-20 17:06+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: none\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: helpdesk_mgmt_merge
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_merge.helpdesk_ticket_merge_view_form
msgid ""
"<span class=\"text-muted\">NB: This will archive the selected tickets "
"(Except the destination ticket)</span>"
msgstr ""
"<span class=\"text-muted\">NB: questo archivierà i ticket selezionati ("
"tranne il ticket di destinazione)</span>"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__user_id
msgid "Assigned to"
msgstr "Assegnato a"

#. module: helpdesk_mgmt_merge
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_merge.helpdesk_ticket_merge_view_form
msgid "Cancel"
msgstr "Annulla"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__create_new_ticket
msgid "Create a new ticket"
msgstr "Crea un nuovo ticket"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__create_date
msgid "Created on"
msgstr "Creato il"

#. module: helpdesk_mgmt_merge
#. odoo-python
#: code:addons/helpdesk_mgmt_merge/wizard/helpdesk_ticket_merge.py:0
#, python-format
msgid "Description from ticket %(name)s: %(description)s"
msgstr "Descrizione dal ticket %(name)s: %(description)s"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__dst_helpdesk_team_id
msgid "Destination Helpdesk Team"
msgstr "Team assistenza clienti destinazione"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: helpdesk_mgmt_merge
#: model:ir.model,name:helpdesk_mgmt_merge.model_helpdesk_ticket_merge
msgid "Helpdesk Ticket Merge"
msgstr "Unione ticket assistenza clienti"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: helpdesk_mgmt_merge
#: model:ir.actions.act_window,name:helpdesk_mgmt_merge.action_helpdesk_ticket_merge
msgid "Merge Helpdesk Tickets"
msgstr "Unisci ticket assistenza clienti"

#. module: helpdesk_mgmt_merge
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_merge.helpdesk_ticket_merge_view_form
msgid "Merge Tickets"
msgstr "Unisci ticket"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__dst_ticket_id
msgid "Merge into an existing ticket"
msgstr "Unisci ad un ticket esistente"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__dst_ticket_name
msgid "New ticket name"
msgstr "Nuovo nome ticket"

#. module: helpdesk_mgmt_merge
#. odoo-python
#: code:addons/helpdesk_mgmt_merge/wizard/helpdesk_ticket_merge.py:0
#, python-format
msgid "No description"
msgstr "Nessuna descrizione"

#. module: helpdesk_mgmt_merge
#: model:ir.model.fields,field_description:helpdesk_mgmt_merge.field_helpdesk_ticket_merge__ticket_ids
msgid "Tickets to Merge"
msgstr "Ticket da unire"
