# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt_timesheet
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2024-11-13 17:06+0000\n"
"Last-Translator: <PERSON> "
"<<EMAIL>>\n"
"Language-Team: none\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
msgid ""
"<span class=\"o_label\">\n"
"                            <i class=\"fa fa-lg fa-play-circle text-"
"success\"/>\n"
"                            Start work\n"
"                        </span>"
msgstr ""
"<span class=\"o_label\">\n"
"                            <i class=\"fa fa-lg fa-play-circle text-"
"success\"/>\n"
"                            Começar a trabalhar\n"
"                        </span>"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
msgid ""
"<span class=\"o_label\">\n"
"                            <i class=\"fa fa-lg fa-stop-circle text-"
"warning\"/>\n"
"                            Stop work\n"
"                        </span>"
msgstr ""
"<span class=\"o_label\">\n"
"                            <i class=\"fa fa-lg fa-stop-circle text-"
"warning\"/>\n"
"                            Pare de trabalhar\n"
"                        </span>"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__allow_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket_team__allow_timesheet
msgid "Allow Timesheet"
msgstr "Permitir Apontamento de Hora"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linha Analitica"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Date"
msgstr "Data"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Duration (Hour(s))"
msgstr "Duração (Hora(s))"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.ticket_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "End work"
msgstr "Fim do Travalho"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Chamado"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_helpdesk_ticket_team
msgid "Helpdesk Ticket Team"
msgstr "Equipe do Chamado"

#. module: helpdesk_mgmt_timesheet
#: model:project.project,name:helpdesk_mgmt_timesheet.project_1
msgid "Helpdesk general project"
msgstr "Projeto geral da Central de Ajuda"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_hr_timesheet_switch
msgid "Helper to quickly switch between timesheet lines"
msgstr ""
"Auxiliar para alternar rapidamente entre as linhas do quadro de horários"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,help:helpdesk_mgmt_timesheet.field_helpdesk_ticket__show_time_control
msgid "Indicate which time control button to show, if any."
msgstr "Indique qual botão de controle de tempo será exibido, se houver."

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__last_timesheet_activity
msgid "Last Timesheet Activity"
msgstr "Última atividade do Apontamento de Hora"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_view_search
msgid "Late"
msgstr "Atrasado"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__planned_hours
msgid "Planned Hours"
msgstr "Horas Planejadas"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__progress
msgid "Progress"
msgstr "Progresso"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__remaining_hours
msgid "Remaining Hours"
msgstr "Horas Restantes"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Resume work"
msgstr "Retomar o trabalho"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__show_time_control
msgid "Show Time Control"
msgstr "Mostrar Controle de Tempo"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.ticket_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Start work"
msgstr "Iniciar Trabalho"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.ticket_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Stop work"
msgstr "Parar trabalho"

#. module: helpdesk_mgmt_timesheet
#: model:project.project,label_tasks:helpdesk_mgmt_timesheet.project_1
msgid "Tasks"
msgstr "Tarefas"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_account_analytic_line__ticket_id
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_timesheets_analysis_report__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_table
msgid "Ticket"
msgstr "Chamado"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_timesheets_analysis_report__ticket_partner_id
msgid "Ticket Partner"
msgstr ""

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_account_analytic_line__ticket_partner_id
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.hr_timesheet_line_search
msgid "Ticket partner"
msgstr "Parceiro do Chamado"

#. module: helpdesk_mgmt_timesheet
#: model:project.project,label_tickets:helpdesk_mgmt_timesheet.project_1
#: model:project.task,label_tickets:helpdesk_mgmt_timesheet.project_task_1
msgid "Tickets"
msgstr "Chamados"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__timesheet_ids
msgid "Timesheet"
msgstr "Apontamento de hora"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_view_search
msgid "Timesheet allowed"
msgstr "Apontamento de hora permitido"

#. module: helpdesk_mgmt_timesheet
#: model:ir.actions.act_window,name:helpdesk_mgmt_timesheet.helpdesk_timesheet_action
#: model:ir.ui.menu,name:helpdesk_mgmt_timesheet.helpdesk_timesheet_menu
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.timesheet_helpdesk_ticket_view_form
msgid "Timesheets"
msgstr "Apontamentos de hora"

#. module: helpdesk_mgmt_timesheet
#: model:ir.model,name:helpdesk_mgmt_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr ""

#. module: helpdesk_mgmt_timesheet
#: model:ir.model.fields,field_description:helpdesk_mgmt_timesheet.field_helpdesk_ticket__total_hours
msgid "Total Hours"
msgstr "Horas Totais"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.helpdesk_ticket_view_search
msgid "With activity today"
msgstr "Com atividade hoje"

#. module: helpdesk_mgmt_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_timesheet.hr_timesheet_line_search
msgid "With ticket"
msgstr "Com chamado"

#~ msgid "Change the Default Project will not have retroactive effects."
#~ msgstr "Alterar o Projeto Padrão não terá efeitos retroativos."

#~ msgid "Display Name"
#~ msgstr "Nome exibido"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Ultima Modificação em"
