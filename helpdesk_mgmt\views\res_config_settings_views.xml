<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app
                    data-string="Helpdesk"
                    string="Helpdesk"
                    name="helpdesk_mgmt"
                    groups="helpdesk_mgmt.group_helpdesk_manager"
                >
                    <block title="New ticket form (portal)">
                        <setting help="Show teams form">
                            <field name="helpdesk_mgmt_portal_select_team" />
                        </setting>
                        <setting>
                            <field
                                name="helpdesk_mgmt_portal_team_id_required"
                                readonly="not helpdesk_mgmt_portal_select_team"
                                string="Required Team"
                            />
                        </setting>
                    </block>
                    <block>
                        <setting>
                            <field
                                name="helpdesk_mgmt_portal_category_id_required"
                                string="Required Category"
                            />
                        </setting>
                    </block>
                </app>
            </xpath>
        </field>
    </record>
    <record id="action_helpdesk_mgmt_config_settings" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'helpdesk_mgmt', 'bin_size': False}</field>
    </record>
</odoo>
