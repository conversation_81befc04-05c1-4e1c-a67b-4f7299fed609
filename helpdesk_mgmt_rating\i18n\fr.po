# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt_rating
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2022-05-31 09:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: helpdesk_mgmt_rating
#: model:mail.template,body_html:helpdesk_mgmt_rating.rating_ticket_email_template
#, fuzzy
msgid ""
"<div>\n"
"                <t t-set=\"access_token\" t-value=\"object."
"_rating_get_access_token()\"/>\n"
"                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                                <p>Hello,</p>\n"
"                                <p>Please take a moment to rate our services "
"related to the ticket \"<strong><t t-out=\"object.number\"/> - <t t-"
"out=\"object.name\"/></strong>\"assigned to <strong>\n"
"                                    <t t-out=\"object._rating_get_operator()."
"name\"/>\n"
"                                </strong>.</p>\n"
"                                <p>We appreciate your feedback. It helps us "
"to improve continuously.</p>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <td style=\"padding:10px 20px\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" "
"style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                                    <tr>\n"
"                                        <td style=\"text-align:center;\">\n"
"                                            <h2 style=\"font-weight:300;font-"
"size:18px;\">Tell us how you feel about our service:</h2>\n"
"                                            <div style=\"text-color: "
"#888888\">(click on one of these smileys)</div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td style=\"padding:10px 10px;\">\n"
"                                            <table style=\"width:100%;text-"
"align:center;margin-top:2rem;\">\n"
"                                                <tr>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/5\">\n"
"                                                            <img "
"alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" "
"title=\"Satisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/3\">\n"
"                                                            <img alt=\"Not "
"satisfied\" src=\"/rating/static/src/img/rating_3.png\" title=\"Not "
"satisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                    <td>\n"
"                                                        <a t-attf-href=\"/"
"rate/{{ access_token }}/1\">\n"
"                                                            <img "
"alt=\"Highly Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" "
"title=\"Highly Dissatisfied\"/>\n"
"                                                        </a>\n"
"                                                    </td>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:auto;text-align:center;"
"font-size:12px;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td style=\"padding-top:10px;color:#afafaf;\">\n"
"                                <t t-if=\"object.rating_status == "
"'stage_change'\">\n"
"                                    <p>This customer survey has been sent "
"because your ticket has been moved to the stage <b t-out=\"object.stage_id."
"name\"/></p>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""
"\n"
"        % set access_token = object.rating_get_access_token()\n"
"        <div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-"
"family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"        <table style=\"width:600px;margin:5px auto;\">\n"
"            <tbody>\n"
"                <tr><td>\n"
"                    <a href=\"/\"><img src=\"/web/binary/company_logo\" "
"style=\"vertical-align:baseline;max-width:100px;\" /></a>\n"
"                </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                        Questionnaire de Satisfaction\n"
"                </td></tr>\n"
"            </tbody>\n"
"        </table>\n"
"        <table style=\"width:600px;margin:0px auto;background:white;"
"border:1px solid #e1e1e1;\">\n"
"          <tbody>\n"
"              <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                  <p>Bonjour,</p>\n"
"                  <p>Merci de prendre quelques instants pour noter nos "
"services liés au ticket \"<strong>${object.number} - ${object.name}</"
"strong>\"\n"
"                     assigné à <strong>${object."
"rating_get_rated_partner_id().name}</strong>.</p>\n"
"                  <p>Merci pour votre retour. Cela nous aide à nous "
"améliorer.</p>\n"
"              </td></tr>\n"
"              <tr><td style=\"padding:10px 20px\">\n"
"                  <table summary=\"o_mail_notification\" style=\"width:100%;"
"border-top:1px solid #e1e1e1;\">\n"
"                      <tr>\n"
"                          <td style=\"text-align:center;\">\n"
"                              <h2 style=\"font-weight:300;font-size:18px;"
"\">\n"
"                                  Dites-nous ce que vous pensez de notre "
"service :\n"
"                              </h2>\n"
"                              <div style=\"text-color: #888888\">(cliquer "
"sur une des émoticones)</div>\n"
"                          </td>\n"
"                      </tr>\n"
"                      <tr>\n"
"                          <td style=\"padding:10px 10px;\">\n"
"                              <table style=\"width:100%;text-align:center;"
"\">\n"
"                                  <tr>\n"
"                                      <td>\n"
"                                          <a href=\"/rate/"
"${access_token}/5\">\n"
"                                              <img alt=\"Satisfied\" src=\"/"
"rating/static/src/img/rating_5.png\" title=\"Satisfait\"/>\n"
"                                          </a>\n"
"                                      </td>\n"
"                                      <td>\n"
"                                          <a href=\"/rate/"
"${access_token}/3\">\n"
"                                              <img alt=\"Not satisfied\" "
"src=\"/rating/static/src/img/rating_3.png\" title=\"Non satisfait\"/>\n"
"                                          </a>\n"
"                                      </td>\n"
"                                      <td>\n"
"                                          <a href=\"/rate/"
"${access_token}/1\">\n"
"                                              <img alt=\"Highly "
"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Très "
"insatisfait\"/>\n"
"                                          </a>\n"
"                                      </td>\n"
"                                  </tr>\n"
"                              </table>\n"
"                          </td>\n"
"                      </tr>\n"
"                  </table>\n"
"              </td></tr>\n"
"              <tr><td style=\"padding:15px 20px 10px 20px;\">${(object."
"user_id.signature or '')| safe}</td></tr>\n"
"          </tbody>\n"
"      </table>\n"
"      <table style=\"width:600px;margin:auto;text-align:center;font-"
"size:12px;\">\n"
"        <tbody>\n"
"          <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
"              % if object.rating_status == 'stage_change':\n"
"                  <p>Ce questionnaire client a été envoyé car votre ticket a "
"changé d'étape vers l'étape  <b>${object.stage_id.name}</b></p>\n"
"              % endif\n"
"          </td></tr>\n"
"        </tbody>\n"
"      </table>\n"
"    </div>\n"
"    "

#. module: helpdesk_mgmt_rating
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.view_helpdesk_ticket_kanban_inherit
msgid ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_avg
msgid "Average Rating"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.actions.act_window,name:helpdesk_mgmt_rating.all_helpdesk_ticket_rating_action
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_status
#: model:ir.ui.menu,name:helpdesk_mgmt_rating.helpdesk_ticket_rating_menu
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.view_helpdesk_ticket_kanban_inherit
msgid "Customer Rating"
msgstr "Satisfaction Client"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__has_message
msgid "Has Message"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model,name:helpdesk_mgmt_rating.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket d'Assistance"

#. module: helpdesk_mgmt_rating
#: model:ir.model,name:helpdesk_mgmt_rating.model_helpdesk_ticket_stage
msgid "Helpdesk Ticket Stage"
msgstr "Étape d'Assistance"

#. module: helpdesk_mgmt_rating
#: model:mail.template,name:helpdesk_mgmt_rating.rating_ticket_email_template
msgid "Helpdesk Ticket: Rating Request"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket_stage__rating_mail_template_id
msgid ""
"If set, an email will be sent to the customer  with a rating survey when the "
"ticket reaches this stage."
msgstr ""
"Si coché, un courriel sera envoyé au client avec un questionnaire de "
"satisfaction quand le ticket passe dans cette étape."

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields.selection,name:helpdesk_mgmt_rating.selection__helpdesk_ticket__rating_status__no_rate
msgid "No rating"
msgstr "Pas de notation"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__positive_rate_percentage
msgid "Positive Rates Percentage"
msgstr "Pourcentage de Satisfaction"

#. module: helpdesk_mgmt_rating
#: model:ir.actions.act_window,name:helpdesk_mgmt_rating.helpdesk_ticket_rating_action
msgid "Rating"
msgstr "Notation"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_avg_text
msgid "Rating Avg Text"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket_stage__rating_mail_template_id
msgid "Rating Email Template"
msgstr "Modèle de Courriel de Satisfaction"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Denier retour de satisfaction"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr "Dernière image de satisfaction"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr "Dernière valeur de satisfaction"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_last_text
msgid "Rating Text"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr "Nombre de réponses"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields.selection,name:helpdesk_mgmt_rating.selection__helpdesk_ticket__rating_status__stage_change
msgid "Rating when changing stage"
msgstr "Satisfaction sur changement d'étape"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__rating_ids
#: model:mail.message.subtype,description:helpdesk_mgmt_rating.mt_ticket_rating
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt_rating.ticket_view_form_inherit
msgid "Ratings"
msgstr "Notations"

#. module: helpdesk_mgmt_rating
#: model:mail.template,subject:helpdesk_mgmt_rating.rating_ticket_email_template
msgid "Satisfaction Survey of the ticket {{object.number}} - {{object.name}}"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model_terms:ir.actions.act_window,help:helpdesk_mgmt_rating.all_helpdesk_ticket_rating_action
msgid "There are no rated tickets at this moment."
msgstr "Il n'y a pas de tickets notés pour le moment."

#. module: helpdesk_mgmt_rating
#: model_terms:ir.actions.act_window,help:helpdesk_mgmt_rating.helpdesk_ticket_rating_action
msgid "There are no ratings for this ticket at the moment."
msgstr "Il n'y a pas de notations pour ce ticket pour le moment."

#. module: helpdesk_mgmt_rating
#. odoo-python
#: code:addons/helpdesk_mgmt_rating/models/helpdesk_ticket.py:0
#: model:mail.message.subtype,name:helpdesk_mgmt_rating.mt_ticket_rating
msgid "Ticket Rating"
msgstr "Notation de Ticket"

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,field_description:helpdesk_mgmt_rating.field_helpdesk_ticket__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: helpdesk_mgmt_rating
#: model:ir.model.fields,help:helpdesk_mgmt_rating.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr ""

#~ msgid "Rating Average"
#~ msgstr "Moyenne de Notation"

#~ msgid "Reason of the rating"
#~ msgstr "Raison de la notation"

#~ msgid "Display Name"
#~ msgstr "Nom Affiché"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Dernière modification le"

#~ msgid "Satisfaction Survey of the ticket ${object.number} - ${object.name}"
#~ msgstr ""
#~ "Questionnaire de Satisfaction pour le ticket ${object.number} - ${object."
#~ "name}"
